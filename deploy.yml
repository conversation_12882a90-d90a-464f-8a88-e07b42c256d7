---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tapd-insight
  labels:
    app: tapd-insight
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tapd-insight
  template:
    metadata:
      labels:
        app: tapd-insight
    spec:
      containers:
      - name: tapd-insight
        image: {{IMAGE}}
        ports:
        - containerPort: 8000
        env:
        - name: DJ<PERSON><PERSON><PERSON>_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: tapd-insight-secrets
              key: django-secret-key
        - name: DJANGO_DEBUG
          value: "0"
        - name: DJANGO_ALLOWED_HOSTS
          value: "*"
        - name: TAPD_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: tapd-insight-secrets
              key: tapd-client-id
        - name: TAPD_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: tapd-insight-secrets
              key: tapd-client-secret
        - name: TAPD_WORKSPACE_ID
          value: "48986738"
        - name: TAPD_LOG_LEVEL
          value: "INFO"
        livenessProbe:
          httpGet:
            path: /
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        volumeMounts:
        - name: static-files
          mountPath: /app/static
      volumes:
      - name: static-files
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: tapd-insight-service
  labels:
    app: tapd-insight
spec:
  selector:
    app: tapd-insight
  ports:
  - name: http
    port: 80
    targetPort: 8000
    protocol: TCP
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: tapd-insight-fat.paykka.com
  labels:
    app: tapd-insight
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - tapd-insight-fat.paykka.com
  rules:
  - host: tapd-insight-fat.paykka.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: tapd-insight-service
            port:
              number: 80

---
apiVersion: v1
kind: Secret
metadata:
  name: tapd-insight-secrets
  labels:
    app: tapd-insight
type: Opaque
stringData:
  django-secret-key: "your-django-secret-key-here"
  tapd-client-id: "your-tapd-client-id"
  tapd-client-secret: "your-tapd-client-secret"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: tapd-insight-config
  labels:
    app: tapd-insight
data:
  TAPD_WORKSPACE_ID: "48986738"
  TAPD_LOG_LEVEL: "INFO"
