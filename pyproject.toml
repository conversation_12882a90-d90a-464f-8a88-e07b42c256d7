[project]
name = "tapd-insight"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
authors = [
    { name = "chengshaojin", email = "<EMAIL>" }
]
requires-python = ">=3.12"
dependencies = [
    "cryptography>=45.0.7",
    "django>=5.0",
    "markdown>=3.5",
    "openpyxl>=3.1.5",
    "pandas>=2.3.2",
    "pymysql>=1.1",
    "requests>=2.32.4",
    "sqlalchemy>=2.0",
]

[project.scripts]
tapd-insight = "tapd_insight:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
