在iteration.py模块中，到目前为止，具备了分析迭代需求维度的信息(get_iteration_stories_overview_markdown方法)，现在需要以人员维度（开发人员、测试人员）分析他们在迭代中的表现。
对于开发人员来说，需要关注：
1、每个人在迭代中所负责开发的需求数量，累计的规模，缺陷的密度、开发延期率、平均规模开发周期（单位1规模的需求开发用时）、

## 迭代研发维度指标prompt
增加H1目录，研发维度分析
研发维度分析增加以下指标：（概览性指标里的人均有问题）
1、概览性指标：人均开发需求数、人均需求规模、人均规模开发时长（单位1规模的需求开发用时）、人均开发延期率、人均缺陷密度、人均缺陷reopen一次比例，人均缺陷reopen一次以上比例。
2、开发人员详细指标数据，表头是开发人员、开发需求数、需求规模、单位1规模开发时长、延期率、缺陷密度、缺陷总数缺陷reopen一次数量、缺陷reopen一次比例、缺陷reopen一次以上数量、缺陷reopen一次以上比例

如果需求没有子需求，以下解释各个指标的计算方法：
1、开发需求数；developers对应的研发（可能有多个）平均拥有需求。如需求A的developers有林向华、孔子维，那么林向华开发需求数0.5，孔子维开发需求数0.5。若developers只有林向华，那么林向华开发需求数1
2、需求规模，对应的开发同上以及规模的分配同第一点。
3、单位1规模开发时长；开发时长取需求的lifetime中技术方案评审、规划中、开发中停留的时长；单位1规模开发时长=开发时长/需求规模。这个值对于参与需求的所有developers都是一样的
4、延期率，即开发延期率；Story类中的dev_is_delayed标识需求是否延期
5、缺陷密度；缺陷密度取迭代内所有需求中，对应开发的缺陷数/这个开发所负责的总需求规模。Story类的bug_stats字段中，有fixer_aggregates字段可以取得开发人员在次需求下的所有缺陷。
6、缺陷总数缺陷reopen一次数量、缺陷reopen一次比例、缺陷reopen一次以上数量、缺陷reopen一次以上比例指标，对应的缺陷取值跟第五点相同。

如果需求有子需求，指标计算方法：
1、开发需求数 需求规模 单位1规模开发时长 延期率 同上
2、缺陷密度、缺陷总数缺陷reopen一次数量、缺陷reopen一次比例、缺陷reopen一次以上数量、缺陷reopen一次以上比例。对应fixer缺陷数量的取值要同时算上需求与子需求。

要求：
1、在demo_iteration_stories_overview调用一个专门计算研发维度指标的函数；
2、不要重复查询需求，最好是demo_iteration_stories_overview中一开始查询出迭代的所有需求以及所需要的所有信息，以下研发维度指标计算的方法仅仅是以迭代中所有需求作为入参