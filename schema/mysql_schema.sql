-- MySQL 8.0 DDL generated from schema/mysql_schema.yaml
-- ----------------------------
-- Table: tapd_stories
-- ----------------------------
CREATE TABLE IF NOT EXISTS `tapd_stories` (
    `workspace_id` varchar(32) NOT NULL COMMENT '工作空间ID',
    `story_id` varchar(32) NOT NULL COMMENT '需求ID',
    `name` varchar(512) NULL COMMENT '需求标题',
    `priority_label` varchar(64) NULL COMMENT '优先级标签',
    `status` varchar(64) NULL COMMENT '当前状态（已做中文映射）',
    `v_status` varchar(64) NULL COMMENT '原始状态值',
    `module` varchar(128) NULL COMMENT '模块',
    `size` decimal(10, 2) NULL COMMENT '规模（故事点）',
    `owner` varchar(128) NULL COMMENT '需求归属（Owner）',
    `tech_lead` varchar(128) NULL COMMENT '研发负责人',
    `test_lead` varchar(128) NULL COMMENT '测试负责人',
    `product_manager` varchar(128) NULL COMMENT '产品经理',
    `iteration_id` varchar(32) NULL COMMENT '迭代ID',
    `category_id` varchar(32) NULL COMMENT '工作项类型ID',
    `ancestor_id` varchar(32) NULL COMMENT '祖先需求ID',
    `parent_id` varchar(32) NULL COMMENT '父需求ID',
    `tech_design_start` datetime NULL COMMENT '技术方案设计开始',
    `tech_design_end` datetime NULL COMMENT '技术方案设计结束',
    `dev_start` datetime NULL COMMENT '开发开始',
    `test_start` datetime NULL COMMENT '测试开始',
    `created` datetime NULL COMMENT '创建时间',
    `review_passed` datetime NULL COMMENT '评审通过时间',
    `begin` datetime NULL COMMENT '预计开始',
    `due` datetime NULL COMMENT '预计结束',
    `planned_test_date` datetime NULL COMMENT '预计提测',
    `actual_test_date` datetime NULL COMMENT '实际提测',
    `planned_test_complete_date` datetime NULL COMMENT '预计测试完成',
    `actual_test_complete_date` datetime NULL COMMENT '实际测试完成',
    `planned_uat_complete_date` datetime NULL COMMENT '预计UAT完成',
    `actual_uat_complete_date` datetime NULL COMMENT '实际UAT完成',
    `planned_release_date` datetime NULL COMMENT '预计上线',
    `effort` decimal(10, 2) NULL COMMENT '工作量估算',
    `effort_completed` decimal(10, 2) NULL COMMENT '已完成工作量',
    `remain` decimal(10, 2) NULL COMMENT '剩余工作量',
    `exceed` decimal(10, 2) NULL COMMENT '超出工作量',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    PRIMARY KEY (`workspace_id`, `story_id`),
    KEY `idx_stories_workspace_created` (`workspace_id`, `created`),
    KEY `idx_stories_workspace_status` (`workspace_id`, `status`),
    KEY `idx_stories_workspace_parent` (`workspace_id`, `parent_id`),
    KEY `idx_stories_workspace_iteration` (
        `workspace_id`,
        `iteration_id`
    )
) ENGINE = InnoDB COMMENT = 'TAPD 需求基础信息';

-- ----------------------------
-- Table: tapd_story_testers
-- ----------------------------
CREATE TABLE IF NOT EXISTS `tapd_story_testers` (
    `workspace_id` varchar(32) NOT NULL,
    `story_id` varchar(32) NOT NULL,
    `tester` varchar(128) NOT NULL,
    PRIMARY KEY (
        `workspace_id`,
        `story_id`,
        `tester`
    ),
    KEY `idx_story_testers_tester` (`tester`)
) ENGINE = InnoDB COMMENT = '需求-测试人员 多对多关系';

-- ----------------------------
-- Table: tapd_story_developers
-- ----------------------------
CREATE TABLE IF NOT EXISTS `tapd_story_developers` (
    `workspace_id` varchar(32) NOT NULL,
    `story_id` varchar(32) NOT NULL,
    `developer` varchar(128) NOT NULL,
    PRIMARY KEY (
        `workspace_id`,
        `story_id`,
        `developer`
    ),
    KEY `idx_story_developers_developer` (`developer`)
) ENGINE = InnoDB COMMENT = '需求-开发者 多对多关系';

-- ----------------------------
-- Table: tapd_story_lifetimes
-- ----------------------------
CREATE TABLE IF NOT EXISTS `tapd_story_lifetimes` (
    `workspace_id` varchar(32) NOT NULL,
    `story_id` varchar(32) NOT NULL,
    `lifetime_id` varchar(64) NOT NULL COMMENT '流转记录ID',
    `status` varchar(64) NULL COMMENT '状态（中文映射后）',
    `owner` varchar(128) NULL,
    `created` datetime NULL,
    `begin_date` datetime NULL,
    `end_date` datetime NULL,
    `time_cost_hours` decimal(12, 2) NULL,
    `operator` varchar(128) NULL,
    `change_from` varchar(64) NULL,
    PRIMARY KEY (
        `workspace_id`,
        `story_id`,
        `lifetime_id`
    ),
    KEY `idx_story_lifetimes_created` (
        `workspace_id`,
        `story_id`,
        `created`
    )
) ENGINE = InnoDB COMMENT = '需求状态流转时间线（来自 measure 接口）';

-- ----------------------------
-- Table: tapd_bugs
-- ----------------------------
CREATE TABLE IF NOT EXISTS `tapd_bugs` (
    `workspace_id` varchar(32) NOT NULL,
    `iteration_id` varchar(32) NULL,
    `story_id` varchar(32) NULL COMMENT '直接关联的需求ID（如有）',
    `bug_id` varchar(32) NOT NULL,
    `title` varchar(512) NULL,
    `priority` varchar(64) NULL,
    `priority_label` varchar(64) NULL,
    `severity` varchar(64) NULL,
    `status` varchar(64) NULL,
    `current_owner` varchar(128) NULL,
    `reporter` varchar(128) NULL,
    `created` datetime NULL,
    `de` varchar(128) NULL COMMENT '开发人员（字段名按 TAPD）',
    `te` varchar(128) NULL COMMENT '测试人员（字段名按 TAPD）',
    `bugtype` varchar(128) NULL,
    `fixer` varchar(128) NULL,
    `closer` varchar(128) NULL,
    `env_raw` varchar(128) NULL COMMENT '环境原始字符串',
    `env_normalized` varchar(32) NULL COMMENT '归一化环境：FAT|UAT|PROD_VERIFY|PROD',
    `flows` text NULL,
    `deadline` datetime NULL,
    `has_reopened` tinyint(1) NOT NULL DEFAULT 0,
    `reopen_times` int NOT NULL DEFAULT 0,
    `is_valid` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否为有效缺陷（非 rejected 关闭）',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`workspace_id`, `bug_id`),
    KEY `idx_bugs_workspace_created` (`workspace_id`, `created`),
    KEY `idx_bugs_workspace_status` (`workspace_id`, `status`),
    KEY `idx_bugs_workspace_reporter` (`workspace_id`, `reporter`),
    KEY `idx_bugs_workspace_fixer` (`workspace_id`, `fixer`),
    KEY `idx_bugs_workspace_env` (
        `workspace_id`,
        `env_normalized`
    )
) ENGINE = InnoDB COMMENT = 'TAPD 缺陷基础信息';

-- 变更脚本：为已存在表增加 is_valid 字段
ALTER TABLE `tapd_bugs`
    ADD COLUMN `is_valid` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否为有效缺陷（非 rejected 关闭）' AFTER `reopen_times`;

-- ----------------------------
-- Table: tapd_iterations
-- ----------------------------
CREATE TABLE IF NOT EXISTS `tapd_iterations` (
    `workspace_id` varchar(32) NOT NULL COMMENT '工作空间ID',
    `iteration_id` varchar(32) NOT NULL COMMENT '迭代ID',
    `name` varchar(512) NULL COMMENT '迭代名称',
    `startdate` date NULL COMMENT '开始日期',
    `enddate` date NULL COMMENT '结束日期',
    `status` varchar(64) NULL COMMENT '状态',
    `creator` varchar(128) NULL COMMENT '创建人',
    `created` datetime NULL COMMENT '创建时间',
    `modified` datetime NULL COMMENT '修改时间',
    `completed` datetime NULL COMMENT '完成时间',
    `description` text NULL COMMENT '描述',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    PRIMARY KEY (
        `workspace_id`,
        `iteration_id`
    ),
    KEY `idx_iterations_workspace_start` (`workspace_id`, `startdate`),
    KEY `idx_iterations_workspace_end` (`workspace_id`, `enddate`),
    KEY `idx_iterations_workspace_status` (`workspace_id`, `status`),
    KEY `idx_iterations_workspace_creator` (`workspace_id`, `creator`)
) ENGINE = InnoDB COMMENT = 'TAPD 迭代基础信息';

-- ----------------------------
-- Table: tapd_sync_checkpoints
-- ----------------------------
CREATE TABLE IF NOT EXISTS `tapd_sync_checkpoints` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `entity` varchar(32) NOT NULL COMMENT '实体类型: story, bug, iteration',
    `workspace_id` varchar(32) NOT NULL,
    `last_created` datetime NULL COMMENT '上次同步的创建时间边界',
    `last_modified` datetime NULL COMMENT '上次同步的修改时间边界',
    `last_id` varchar(64) NULL COMMENT '上次同步的最大ID或游标',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_checkpoint_entity_ws` (`entity`, `workspace_id`)
) ENGINE = InnoDB COMMENT = '定时同步检查点';