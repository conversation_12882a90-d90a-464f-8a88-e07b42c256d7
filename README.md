## 指标

### 工作负载

产品

- 需求数量 / 人均需求数
- 需求变更率
- 需求延期率
- 评审被驳回率

研发

- 人均开发需求数、人均 Story Point 完成数
- 人均规模开发时长（单位 1 规模的需求开发用时）、人均开发延期率
- 人均修复缺陷数、缺陷修复时长、缺陷 reopen 数

测试

- 人均测试需求数
- 缺陷提交数 / 人均缺陷提交数
- 测试用例执行数、覆盖率
- 缺陷验证时长、reopen 数

### 工作质量

需求质量

- 需求返工率
- 需求变更率
- 需求验收缺陷率

研发质量

- 缺陷密度（缺陷数 / 需求数或 Story Point）
- 修复一次通过率
- 缺陷 reopen 率
- 线上缺陷率

测试质量

- 缺陷发现率（测试阶段发现缺陷 / 总缺陷）
- 遗漏缺陷率（线上缺陷 / 总缺陷）
- 缺陷有效率（有效缺陷 / 提交缺陷）

## 基础数据收集

### 需求

#### 基础字段

```python
id: str # 需求id
name: Optional[str] # 需求名称
priority_label: Optional[str] # 优先级 High...
status: Optional[str] # 状态 planning...
module: Optional[str] # 模块 COLL-portal产品
size: Optional[str]  # 规模 故事点
owner: Optional[str] # 当前处理人
developer: List[str] # 开发人员，可能有多个值
iteration_id: Optional[str] # 迭代id
category_id: Optional[str] # 需求分类id 支持枚举查询
ancestor_id: Optional[str] # 祖先需求，可查询指定需求下所有子需求
parent_id: Optional[str] # 父需求id，没有父需求时为0
children_id: Optional[list] # 子需求id ||1148986738001066096|1148986738001066097|1148986738001066098
workspace_id: Optional[str] # 工作空间id
custom_field_one: Optional[str] # 测试人员 多值 陈广发;毛浩;
custom_field_six: Optional[str] # 研发负责人 多值 孔子维, 但一般为单值
developer: List[str] # 开发人员，可能有多个值 梁鹏屿;钟嘉峻;孔子维;李嘉豪;蒋政;
custom_field_19: Optional[str] # 产品经理 多值 刘雨乐;钟巧; 但一般为单值
created: Optional[str]  # 创建时间  2025-08-07 09:39:57
begin: Optional[str]  # 预计开始  2025-07-25 None
due: Optional[str]  # 预计结束  2025-08-25 None
custom_field_10: Optional[str] # 预计提测 2025-08-08
custom_field_15: Optional[str] # 实际提测 2025-08-09
custom_field_13: Optional[str] # 预计测试完成 2025-08-12
custom_field_17: Optional[str] # 实际测试完成 2025-08-14 09:57
custom_field_14: Optional[str] # 预计UAT完成 2025-08-13
custom_field_16: Optional[str] # 实际UAT完成 2025-08-15
custom_field_11: Optional[str] # 预计上线 2025-08-14
custom_field_18: Optional[str] # 需求评审通过 2025-08-19 14:05
effort: Optional[float]  # 工作量估算
effort_completed: Optional[float]  # 已完成工作量
remain: Optional[float]  # 剩余工作量
exceed: Optional[float]  # 超出工作量
```

需求的导出属性
缺陷情况：

```python
fat_bugs: int # fat 环境缺陷数
uat_bugs: int # uat 环境缺陷数
prod_bugs: int # 生产环境缺陷数
prod_verify_bugs: int # 生产环境验证缺陷数
valid_bugs: int # 有效缺陷数
```

### 缺陷

#### 基础字段

```python
id: str # 缺陷id
title: Optional[str] # 缺陷标题
priority_label: Optional[str] # 优先级 High...
priority: Optional[str] # 优先级 High...
severity: Optional[str] # 严重等级 prompt...
status: Optional[str] # 状态 closed...
story_id: Optional[str] # 关联需求id, 可能为空
iteration_id: Optional[str] # 迭代id
current_owner: Optional[str] # 当前处理人
reporter: Optional[str] # 缺陷报告人
created: Optional[str] # 创建时间 2025-08-07 09:39:57
de: Optional[str] # 开发人员
te: Optional[str] # 测试人员
bugtype: Optional[str] # 缺陷类型 功能缺陷;性能缺陷;安全缺陷;UI缺陷;需求缺陷;其他
fixer: Optional[str] # 修复人，也标识缺陷的开发人员
closer: Optional[str] # 关闭人
workspace_id: str # 工作空间id
custom_field_one: Optional[str] # 环境 生产环境;UAT环境;FAT环境;生产验证环境
flows: Optional[str] # 流程 new|rejected|closed 或者 new|in_progress|resolved|planning|closed
deadline: Optional[str] # 截止时间 2025-08-25
```

运行方式：
uv run manage.py runserver 0.0.0.0:8000
访问
首页: http://localhost:8000/
报表: http://localhost:8000/report/iteration/?iteration_id=1148986738001001961
