"""
TAPD API 传输层：统一请求、调试打印与 429 阶梯退避重试。

使用 requests.Session 以复用连接。
"""

from __future__ import annotations

import time
import random
from typing import Any, Dict, Optional
import logging

import requests


class TapdApiTransport:
    """统一的 TAPD API HTTP 传输层，内置 429 处理与退避重试。"""

    def __init__(
        self,
        *,
        request_timeout_seconds: float = 10.0,
        max_retries: int = 5,
        backoff_initial_seconds: float = 2.0,
        backoff_factor: float = 2.0,
        backoff_max_seconds: float = 60.0,
        min_interval_seconds: float = 1.1,
        respect_rate_pacing: bool = True,
        debug_logging: bool = True,
    ) -> None:
        self._session = requests.Session()
        self._timeout = request_timeout_seconds
        self._max_retries = max_retries
        self._backoff_initial = backoff_initial_seconds
        self._backoff_factor = backoff_factor
        self._backoff_max = backoff_max_seconds
        self._min_interval = max(0.0, float(min_interval_seconds))
        self._respect_pacing = respect_rate_pacing
        self._debug = debug_logging
        self._last_request_monotonic: float = 0.0
        self._logger = logging.getLogger("tapd_insight.transport")

    def request(
        self,
        method: str,
        url: str,
        *,
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        json: Optional[Any] = None,
    ) -> requests.Response:
        """发送请求，遇到 429 实施阶梯等待重试；5xx 也按相同策略重试。"""
        attempt = 0
        last_exc: Optional[Exception] = None

        while attempt <= self._max_retries:
            # 基于 TAPD 60 req/min 的节流，默认设置最小 1.1s 间隔
            if self._respect_pacing and self._min_interval > 0:
                now = time.monotonic()
                if self._last_request_monotonic > 0:
                    delta = now - self._last_request_monotonic
                    if delta < self._min_interval:
                        sleep_s = self._min_interval - delta
                        if self._debug:
                            self._logger.debug(f"节流等待 {sleep_s:.3f}s 以满足最小间隔 {self._min_interval:.3f}s")
                        time.sleep(sleep_s)
            if self._debug:
                masked_headers = self._mask_sensitive_headers(headers or {})
                # 请求日志：info；参数与头：debug
                self._logger.info(f"{method.upper()} {url}")
                if params:
                    self._logger.debug(f"Params: {params}")
                if data:
                    self._logger.debug(f"Data: {data}")
                if json is not None:
                    self._logger.debug("JSON: <omitted>")
                self._logger.debug(f"Headers: {masked_headers}")

            try:
                resp = self._session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    params=params,
                    data=data,
                    json=json,
                    timeout=self._timeout,
                )

                status = resp.status_code
                if status == 429 or 500 <= status < 600:
                    # 429 或 5xx：读取 Retry-After 或按退避策略等待后重试
                    wait_seconds = self._compute_wait_seconds(attempt, resp)
                    if attempt < self._max_retries:
                        if self._debug:
                            self._logger.warning(f"status={status}, 等待 {wait_seconds:.1f}s 后重试 第{attempt+1}次")
                        time.sleep(wait_seconds)
                        attempt += 1
                        continue
                    # 超过重试次数，抛错
                    resp.raise_for_status()

                # 非 429/5xx 或已成功
                try:
                    resp.raise_for_status()
                except requests.exceptions.HTTPError:
                    self._logger.error(f"请求失败 {method.upper()} {url} status={resp.status_code}")
                    raise
                self._last_request_monotonic = time.monotonic()
                # 响应：状态 info，正文 debug
                self._logger.info(f"响应 {status} {method.upper()} {url}")
                if self._debug:
                    try:
                        self._logger.debug(f"Response JSON: {resp.text}")
                    except Exception:
                        self._logger.debug("Response JSON: <unavailable>")
                return resp

            except requests.exceptions.RequestException as exc:
                last_exc = exc
                # 连接类错误也按退避策略重试
                wait_seconds = self._compute_wait_seconds(attempt, None)
                if attempt < self._max_retries:
                    if self._debug:
                        self._logger.warning(f"网络异常: {exc}. 等待 {wait_seconds:.1f}s 后重试 第{attempt+1}次")
                    time.sleep(wait_seconds)
                    attempt += 1
                    continue
                raise

        # 理论上不会到达这里
        if last_exc:
            raise last_exc
        raise RuntimeError("请求失败且未捕获异常")

    def _compute_wait_seconds(self, attempt: int, resp: Optional[requests.Response]) -> float:
        # Retry-After / X-RateLimit-Reset(-After) 优先
        if resp is not None:
            for header_name in ("Retry-After", "X-RateLimit-Reset-After", "X-RateLimit-Reset"):
                header_val = resp.headers.get(header_name)
                if header_val:
                    try:
                        seconds = float(header_val)
                        return max(0.0, min(seconds, self._backoff_max))
                    except ValueError:
                        continue
        # 指数退避 + 抖动，并确保不小于最小请求间隔
        base = min(self._backoff_initial * (self._backoff_factor ** attempt), self._backoff_max)
        base = max(base, self._min_interval)
        jitter = random.uniform(0, 0.5)
        return base + jitter

    @staticmethod
    def _mask_sensitive_headers(headers: Dict[str, str]) -> Dict[str, str]:
        masked = dict(headers)
        auth = masked.get("Authorization")
        if not auth:
            return masked
        if auth.startswith("Basic "):
            masked["Authorization"] = "Basic ***MASKED***"
        elif auth.startswith("Bearer "):
            token = auth[7:]
            if len(token) > 8:
                masked["Authorization"] = f"Bearer {token[:4]}***{token[-4:]}"
            else:
                masked["Authorization"] = "Bearer ***MASKED***"
        return masked


