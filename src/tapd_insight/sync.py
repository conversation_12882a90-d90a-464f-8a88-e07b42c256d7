"""
数据同步模块：按时间范围增量拉取 TAPD 迭代、需求、缺陷与关联数据，写入 MySQL。

说明：
- 使用 SQLAlchemy ORM（请先安装 sqlalchemy、pymysql）
- upsert 策略：按 (workspace_id, id) 复合键更新
- 检查点：表 tapd_sync_checkpoints 记录每类实体最近同步边界
"""

from __future__ import annotations

from typing import List, Tuple, Optional, Dict, Any
from datetime import datetime, timedelta
import logging
from .time_utils import (
    filter_items_by_time_field,
    compute_min_datetime_in_items,
    parse_datetime_bound,
)

from .auth import TapdClientCredentialsAuth
from .config import load_tapd_config
from .story import TapdStoryClient, Story
from .bug import TapdBugClient, Bug
from .iteration import TapdIterationClient, Iteration
from .persistence.db import create_session_factory, session_scope
from .persistence.models import (
    TapdStory,
    TapdStoryTester,
    TapdStoryDeveloper,
    TapdStoryLifetime,
    TapdBug,
    TapdIteration,
    TapdSyncCheckpoint,
)


logger = logging.getLogger("tapd_insight.sync")

def _to_dt(date_str: Optional[str]) -> Optional[datetime]:
    """将多种常见时间文本解析为 datetime 对象。

    支持格式：
    - YYYY-MM-DD
    - YYYY-MM-DD HH:MM
    - YYYY-MM-DD HH:MM:SS
    解析失败返回 None。
    """
    if not date_str:
        return None
    s = str(date_str).strip()
    if not s:
        return None
    formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M",
        "%Y-%m-%d",
    ]
    for fmt in formats:
        # 截断为对应格式的最短长度，避免多余字符导致解析失败
        need_len = 19 if fmt == "%Y-%m-%d %H:%M:%S" else (16 if fmt == "%Y-%m-%d %H:%M" else 10)
        if len(s) >= need_len:
            try:
                return datetime.strptime(s[:need_len], fmt)
            except Exception:
                pass
    return None


def _upsert_story(session, workspace_id: str, s: Story) -> bool:
    obj = session.get(TapdStory, {"workspace_id": workspace_id, "story_id": s.id})
    created_new = False
    if obj is None:
        obj = TapdStory(workspace_id=workspace_id, story_id=s.id)
        session.add(obj)
        created_new = True
    obj.name = s.name
    obj.priority_label = s.priority_label
    obj.status = s.status
    obj.v_status = s.v_status
    obj.module = s.module
    obj.size = float(s.size) if s.size is not None else None
    obj.owner = s.owner
    obj.tech_lead = s.tech_lead
    obj.test_lead = s.test_lead
    obj.product_manager = getattr(s, "product_manager", None)
    obj.iteration_id = s.iteration_id
    obj.category_id = s.category_id
    obj.ancestor_id = s.ancestor_id
    obj.parent_id = s.parent_id
    obj.tech_design_start = _to_dt(getattr(s, "tech_design_start", None))
    obj.tech_design_end = _to_dt(getattr(s, "tech_design_end", None))
    obj.dev_start = _to_dt(getattr(s, "dev_start", None))
    obj.test_start = _to_dt(getattr(s, "test_start", None))
    obj.created = _to_dt(s.created)
    obj.review_passed = _to_dt(getattr(s, "review_passed", None))
    obj.begin = _to_dt(s.begin)
    obj.due = _to_dt(s.due)
    obj.planned_test_date = _to_dt(getattr(s, "planned_test_date", None))
    obj.actual_test_date = _to_dt(getattr(s, "actual_test_date", None))
    obj.planned_test_complete_date = _to_dt(getattr(s, "planned_test_complete_date", None))
    obj.actual_test_complete_date = _to_dt(getattr(s, "actual_test_complete_date", None))
    obj.planned_uat_complete_date = _to_dt(getattr(s, "planned_uat_complete_date", None))
    obj.actual_uat_complete_date = _to_dt(getattr(s, "actual_uat_complete_date", None))
    obj.planned_release_date = _to_dt(getattr(s, "planned_release_date", None))

    # 关系：测试人员
    session.query(TapdStoryTester).filter_by(workspace_id=workspace_id, story_id=s.id).delete()
    for t in getattr(s, "testers", []) or []:
        session.add(TapdStoryTester(workspace_id=workspace_id, story_id=s.id, tester=str(t)))

    # 关系：开发者
    session.query(TapdStoryDeveloper).filter_by(workspace_id=workspace_id, story_id=s.id).delete()
    for d in getattr(s, "developers", []) or []:
        session.add(TapdStoryDeveloper(workspace_id=workspace_id, story_id=s.id, developer=str(d)))

    # 生命周期（仅当对象含 lifetimes）
    if getattr(s, "lifetimes", None):
        session.query(TapdStoryLifetime).filter_by(workspace_id=workspace_id, story_id=s.id).delete()
        for lt in s.lifetimes:
            session.add(TapdStoryLifetime(
                workspace_id=workspace_id,
                story_id=s.id,
                lifetime_id=str(getattr(lt, "id", "")),
                status=getattr(lt, "status", None),
                owner=getattr(lt, "owner", None),
                created=_to_dt(getattr(lt, "created", None)),
                begin_date=getattr(lt, "begin_date", None),
                end_date=getattr(lt, "end_date", None),
                time_cost_hours=float(getattr(lt, "time_cost_hours", 0.0)) if getattr(lt, "time_cost_hours", None) is not None else None,
                operator=getattr(lt, "operator", None),
                change_from=getattr(lt, "change_from", None),
            ))
    return created_new


def _upsert_bug(session, workspace_id: str, b: Bug) -> bool:
    obj = session.get(TapdBug, {"workspace_id": workspace_id, "bug_id": b.id})
    created_new = False
    if obj is None:
        obj = TapdBug(workspace_id=workspace_id, bug_id=b.id)
        session.add(obj)
        created_new = True
    obj.iteration_id = b.iteration_id
    obj.story_id = b.story_id
    obj.title = b.title
    obj.priority = b.priority
    obj.priority_label = b.priority_label
    obj.severity = b.severity
    obj.status = b.status
    obj.current_owner = b.current_owner
    obj.reporter = b.reporter
    obj.created = _to_dt(b.created)
    obj.de = b.de
    obj.te = b.te
    obj.bugtype = b.bugtype
    obj.fixer = b.fixer
    obj.closer = b.closer
    obj.env_raw = b.env
    obj.env_normalized = b.get_environment()
    obj.flows = b.flows
    obj.deadline = _to_dt(b.deadline)
    obj.has_reopened = bool(getattr(b, "has_reopened", False))
    obj.reopen_times = int(getattr(b, "reopen_times", 0) or 0)
    # 有效性：由领域对象方法判断并落库
    try:
        obj.is_valid = bool(not b.is_invalid())
    except Exception:
        obj.is_valid = None
    return created_new


def _upsert_story_bug_rel(session, workspace_id: str, story_id: str, bug_id: str, relation_type: Optional[str]) -> None:
    # 已弃用：bug->story 关系直接通过 TapdBug.story_id 维护
    return None


def _upsert_iteration(session, workspace_id: str, it: Iteration) -> bool:
    """
    插入或更新迭代信息到数据库。

    该方法实现了 upsert 操作（insert or update）：
    1. 首先尝试根据主键（workspace_id + iteration_id）查找现有记录
    2. 如果记录不存在，创建新的 TapdIteration 对象并添加到会话
    3. 无论是新建还是更新，都会设置所有字段的最新值
    4. 日期字段通过 _to_dt 函数进行格式转换

    Args:
        session: SQLAlchemy 数据库会话对象
        workspace_id: TAPD 工作空间ID
        it: 从 TAPD API 获取的迭代对象
    """
    obj = session.get(TapdIteration, {"workspace_id": workspace_id, "iteration_id": it.id})
    created_new = False
    if obj is None:
        obj = TapdIteration(workspace_id=workspace_id, iteration_id=it.id)
        session.add(obj)
        created_new = True
    obj.name = it.name
    obj.startdate = _to_dt(it.startdate)
    obj.enddate = _to_dt(it.enddate)
    obj.status = it.status
    obj.creator = it.creator
    obj.created = _to_dt(it.created)
    obj.modified = _to_dt(it.modified)
    obj.completed = _to_dt(it.completed)
    obj.description = it.description
    return created_new


def _get_checkpoint(session, workspace_id: str, entity: str) -> TapdSyncCheckpoint | None:
    return session.query(TapdSyncCheckpoint).filter_by(workspace_id=workspace_id, entity=entity).one_or_none()


def _save_checkpoint(session, workspace_id: str, entity: str, last_created: Optional[datetime], last_id: Optional[str]) -> None:
    ck = _get_checkpoint(session, workspace_id, entity)
    if ck is None:
        ck = TapdSyncCheckpoint(entity=entity, workspace_id=workspace_id)
        session.add(ck)
    ck.last_created = last_created
    ck.last_id = last_id


def run_incremental_sync(db_url: str, days: int = 7) -> None:
    """增量同步最近 N 天的迭代、需求、缺陷及关联关系。

    优化点：
    - 分开处理迭代、需求、缺陷，每类独立事务与日志
    - 按页批量拉取，直到无数据为止
    - 输出流程日志
    """
    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(client_id=cfg.client_id, client_secret=cfg.client_secret)

    story_client = TapdStoryClient(auth)
    bug_client = TapdBugClient(auth)
    iteration_client = TapdIterationClient(auth)

    Session = create_session_factory(db_url)

    start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
    end_date = datetime.now().strftime("%Y-%m-%d")

    logger.info(f"[SYNC] 开始增量同步: days={days}, range={start_date}..{end_date}")
    # 提示：首次运行请确保已执行 schema/mysql_schema.sql 或使用 ORM 自动建表

    # 迭代：分页拉取 + 端侧过滤（按 created）
    logger.info("[SYNC][ITER] 开始同步迭代")
    page = 1
    total_iter = 0
    created_iter = 0
    start_dt = parse_datetime_bound(start_date, default_to_start=True)
    while True:
        page_items = iteration_client.get_iterations(
            workspace_id=cfg.workspace_id,
            fields=None,
            limit=200,
            page=page,
            order="created desc",
        )
        if not page_items:
            break
        filtered, violations = filter_items_by_time_field(
            page_items, field_name="created", start_text=start_date, end_text=end_date
        )
        with session_scope(Session) as session:
            page_created = 0
            for it_raw in filtered:
                it_obj = Iteration.from_dict(it_raw)
                if _upsert_iteration(session, cfg.workspace_id, it_obj):
                    page_created += 1
            total_iter += len(filtered)
            created_iter += page_created
            logger.info(f"[SYNC][ITER] page={page} rows={len(filtered)} (raw={len(page_items)}) created={page_created} (cum total={total_iter})")
        # 提前停止
        min_created = compute_min_datetime_in_items(page_items, field_name="created")
        if start_dt is not None and min_created is not None and min_created < start_dt:
            break
        if len(page_items) < 200:
            break
        page += 1
    with session_scope(Session) as session:
        _save_checkpoint(session, cfg.workspace_id, "iteration", _to_dt(end_date), None)
    logger.info(f"[SYNC][ITER] 完成，共处理 {total_iter} 条，新增 {created_iter} 条")

    # 需求：分页拉取，包含 lifetimes
    logger.info("[SYNC][STORY] 开始同步需求与生命周期")
    page = 1
    total_story = 0
    created_story = 0
    total_rel = 0
    while True:
        stories = story_client.get_stories_by_created_range(
            workspace_id=cfg.workspace_id,
            created_start=start_date,
            created_end=end_date,
            include_bug_stats=False,
            include_life_times=True,
            limit=200,
            page=page,
        )
        if not stories:
            break
        with session_scope(Session) as session:
            page_created = 0
            page_rel = 0
            for s in stories:
                if _upsert_story(session, cfg.workspace_id, s):
                    page_created += 1
            # 需求-缺陷关系：已废弃，TAPD 约束一个缺陷只能关联一个需求，直接使用 bug 表的 story_id 字段
            # 此处仅统计目的保留 page_rel=0
            page_rel = 0
            total_story += len(stories)
            created_story += page_created
            total_rel += page_rel
            logger.info(f"[SYNC][STORY] page={page} rows={len(stories)} created={page_created} rel={page_rel} (cum total={total_story}, rel_total={total_rel})")
        page += 1
    with session_scope(Session) as session:
        _save_checkpoint(session, cfg.workspace_id, "story", _to_dt(end_date), None)
    logger.info(f"[SYNC][STORY] 完成，共处理 {total_story} 条，新增 {created_story} 条，关系 {total_rel} 条")

    # 缺陷：分页拉取
    logger.info("[SYNC][BUG] 开始同步缺陷")
    page = 1
    total_bug = 0
    created_bug = 0
    synced_bug_ids: List[str] = []
    while True:
        bugs = bug_client.get_bugs_by_created_range(
            workspace_id=cfg.workspace_id,
            created_start=start_date,
            created_end=end_date,
            limit=200,
            page=page,
        )
        if not bugs:
            break
        with session_scope(Session) as session:
            page_created = 0
            for b in bugs:
                if _upsert_bug(session, cfg.workspace_id, b):
                    page_created += 1
                synced_bug_ids.append(str(b.id))
            total_bug += len(bugs)
            created_bug += page_created
            logger.info(f"[SYNC][BUG] page={page} rows={len(bugs)} created={page_created} (cum total={total_bug})")
        page += 1
    with session_scope(Session) as session:
        _save_checkpoint(session, cfg.workspace_id, "bug", _to_dt(end_date), None)
    logger.info(f"[SYNC][BUG] 完成，共处理 {total_bug} 条，新增 {created_bug} 条")

    # 缺陷关联需求：批量查询并更新 TapdBug.story_id
    if synced_bug_ids:
        logger.info(f"[SYNC][BUG->STORY] 开始处理缺陷关联的需求，缺陷数={len(synced_bug_ids)}")
        bug_client = TapdBugClient(auth)
        # 分批以避免 URL 过长
        batch_size = 200
        updated_count = 0
        for i in range(0, len(synced_bug_ids), batch_size):
            batch = synced_bug_ids[i:i + batch_size]
            relations = bug_client.get_related_stories(workspace_id=cfg.workspace_id, bug_id=batch)
            # 建立 bug_id -> story_id 映射（若返回多个，取第一个）
            bug_to_story: Dict[str, str] = {}
            for r in relations:
                bid = str(r.bug_id)
                if bid not in bug_to_story:
                    bug_to_story[bid] = str(r.story_id)
            if not bug_to_story:
                continue
            with session_scope(Session) as session:
                for bid, sid in bug_to_story.items():
                    obj = session.get(TapdBug, {"workspace_id": cfg.workspace_id, "bug_id": bid})
                    if obj is None:
                        continue
                    # 仅当为空或不同才更新
                    if obj.story_id != sid:
                        obj.story_id = sid
                        updated_count += 1
        logger.info(f"[SYNC][BUG->STORY] 关联需求更新完成，更新 {updated_count} 条")


def run_sync_by_iteration_ids(db_url: str, iteration_ids: List[str]) -> None:
    """
    根据给定的迭代ID列表，从 TAPD 拉取迭代、需求（含生命周期）、缺陷等数据并写入本地数据库。

    特点：
    - 精确范围：仅处理指定迭代ID范围内的数据
    - 需求：拉取迭代内所有需求（父/子均可由 TAPD 返回），并写入生命周期、测试人员、开发者关系
    - 缺陷：优先按 iteration_id 拉取；若接口不支持或无数据，回退为基于需求关系拉取
    - 关系：以缺陷表的 story_id 字段为准，必要时通过关联关系回填
    """
    if not iteration_ids:
        logger.info("[SYNC][BY_ITER] 传入的迭代ID列表为空，跳过处理")
        return

    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(client_id=cfg.client_id, client_secret=cfg.client_secret)

    story_client = TapdStoryClient(auth)
    bug_client = TapdBugClient(auth)
    iteration_client = TapdIterationClient(auth)

    Session = create_session_factory(db_url)

    # 逐个迭代处理，便于定位问题并输出明细日志
    for iter_id in iteration_ids:
        iter_id_str = str(iter_id)
        logger.info(f"[SYNC][BY_ITER] 开始处理迭代 id={iter_id_str}")

        # 1) 迭代：查询并 upsert
        try:
            it_raw = iteration_client.get_iteration_by_id(workspace_id=cfg.workspace_id, iteration_id=iter_id_str)
            if it_raw is None:
                logger.warning(f"[SYNC][BY_ITER] 迭代不存在或不可见 id={iter_id_str}")
            else:
                it_obj = Iteration.from_dict(it_raw)
                with session_scope(Session) as session:
                    created = _upsert_iteration(session, cfg.workspace_id, it_obj)
                    logger.info(f"[SYNC][BY_ITER][ITER] upsert iteration id={iter_id_str} created={created}")
        except Exception as e:
            logger.exception(f"[SYNC][BY_ITER][ITER] 获取或写入迭代失败 id={iter_id_str}")
            continue

        # 2) 需求：根据 iteration_id 拉取，包含生命周期
        stories: List[Story] = []
        page = 1
        page_size = 200
        while True:
            try:
                page_items = story_client.get_stories_as_objects(
                    workspace_id=cfg.workspace_id,
                    fields=None,
                    limit=page_size,
                    page=page,
                    order="created desc",
                    params={"iteration_id": iter_id_str},
                    include_bug_stats=False,
                    include_life_times=True,
                )
            except Exception:
                logger.exception(f"[SYNC][BY_ITER][STORY] 拉取需求失败 id={iter_id_str} page={page}")
                break
            if not page_items:
                break
            stories.extend(page_items)
            with session_scope(Session) as session:
                page_created = 0
                for s in page_items:
                    if _upsert_story(session, cfg.workspace_id, s):
                        page_created += 1
                logger.info(f"[SYNC][BY_ITER][STORY] iter={iter_id_str} page={page} rows={len(page_items)} created={page_created}")
            if len(page_items) < page_size:
                break
            page += 1
        logger.info(f"[SYNC][BY_ITER][STORY] iter={iter_id_str} 总计拉取 {len(stories)} 条")

        # 3) 缺陷：先通过需求关系获取并写入；再按迭代拉取其余缺陷并写入（去重）
        synced_bug_ids: List[str] = []
        processed_bug_ids: set[str] = set()

        # 3.a 基于需求关系获取缺陷并 upsert
        if stories:
            story_ids = [str(s.id) for s in stories]
            try:
                relations = story_client.get_related_bugs(workspace_id=cfg.workspace_id, story_id=story_ids)
            except Exception:
                logger.exception(f"[SYNC][BY_ITER][BUG_REL] 获取需求关联的缺陷关系失败 iter={iter_id_str}")
                relations = []
            bug_ids: List[str] = []
            for r in relations:
                bid = str(getattr(r, "bug_id", ""))
                if bid and bid not in processed_bug_ids:
                    processed_bug_ids.add(bid)
                    bug_ids.append(bid)
            logger.info(f"[SYNC][BY_ITER][BUG_REL] iter={iter_id_str} 通过关系获取到 bug_id 数={len(bug_ids)}")
            # 分批获取缺陷详情并 upsert（批量接口）
            batch_size = 200
            for i in range(0, len(bug_ids), batch_size):
                batch = bug_ids[i:i + batch_size]
                try:
                    bugs_batch = bug_client.get_bugs_by_ids_as_objects(
                        workspace_id=cfg.workspace_id,
                        bug_ids=batch,
                    )
                except Exception:
                    logger.exception(f"[SYNC][BY_ITER][BUG_REL] 批量获取缺陷失败 iter={iter_id_str} batch_index={i//batch_size+1}")
                    bugs_batch = []
                with session_scope(Session) as session:
                    page_created = 0
                    for b in bugs_batch:
                        if _upsert_bug(session, cfg.workspace_id, b):
                            page_created += 1
                        synced_bug_ids.append(str(b.id))
                logger.info(f"[SYNC][BY_ITER][BUG_REL] iter={iter_id_str} batch upsert created={page_created}")

        # 3.b 按迭代拉取其余缺陷（去重 processed_bug_ids）
        page = 1
        page_size = 200
        while True:
            try:
                bug_page = bug_client.get_bugs_as_objects(
                    workspace_id=cfg.workspace_id,
                    fields=None,
                    limit=page_size,
                    page=page,
                    order="created desc",
                    params={"iteration_id": iter_id_str},
                )
            except Exception:
                logger.exception(f"[SYNC][BY_ITER][BUG_ITER] 拉取缺陷失败 iter={iter_id_str} page={page}")
                break
            if not bug_page:
                break
            with session_scope(Session) as session:
                page_created = 0
                skipped = 0
                # 仅处理不在 processed_bug_ids 中的缺陷
                to_upsert: List[Bug] = []  # type: ignore[name-defined]
                for b in bug_page:
                    bid = str(b.id)
                    if bid in processed_bug_ids:
                        skipped += 1
                        continue
                    to_upsert.append(b)
                    processed_bug_ids.add(bid)
                for b in to_upsert:
                    if _upsert_bug(session, cfg.workspace_id, b):
                        page_created += 1
                    synced_bug_ids.append(str(b.id))
                logger.info(f"[SYNC][BY_ITER][BUG_ITER] iter={iter_id_str} page={page} rows={len(bug_page)} created={page_created} skipped={skipped}")
            if len(bug_page) < page_size:
                break
            page += 1

        # 4) 回填缺陷的 story_id（若缺失或不同），与 run_incremental_sync 一致
        if synced_bug_ids:
            try:
                logger.info(f"[SYNC][BY_ITER][BUG->STORY] 处理缺陷关联的需求，缺陷数={len(synced_bug_ids)}")
                bug_client = TapdBugClient(auth)
                batch_size = 200
                updated_count = 0
                for i in range(0, len(synced_bug_ids), batch_size):
                    batch = synced_bug_ids[i:i + batch_size]
                    relations = bug_client.get_related_stories(workspace_id=cfg.workspace_id, bug_id=batch)
                    bug_to_story: Dict[str, str] = {}
                    for r in relations:
                        bid = str(r.bug_id)
                        if bid not in bug_to_story:
                            bug_to_story[bid] = str(r.story_id)
                    if not bug_to_story:
                        continue
                    with session_scope(Session) as session:
                        for bid, sid in bug_to_story.items():
                            obj = session.get(TapdBug, {"workspace_id": cfg.workspace_id, "bug_id": bid})
                            if obj is None:
                                continue
                            if obj.story_id != sid:
                                obj.story_id = sid
                                updated_count += 1
                logger.info(f"[SYNC][BY_ITER][BUG->STORY] 关联需求更新完成，更新 {updated_count} 条")
            except Exception:
                logger.exception(f"[SYNC][BY_ITER][BUG->STORY] 处理缺陷关联需求失败 iter={iter_id_str}")

        logger.info(f"[SYNC][BY_ITER] 完成处理迭代 id={iter_id_str}")


def demo_run_sync() -> None:
    """演示：运行一次增量同步最近7天数据。"""
    from .config import load_tapd_config

    cfg = load_tapd_config()
    db_url = "mysql+pymysql://team_seek:seek#<EMAIL>:3306/team_seek"
    # db_url = "mysql+pymysql://root:123456@127.0.0.1:3306/tapd"
    logger.warning("请根据本地数据库设置修改 db_url 后再运行 demo_run_sync()")
    run_incremental_sync(db_url=db_url, days=14)


def demo_run_sync_by_iteration_ids() -> None:
    """
    演示：按指定迭代ID列表同步数据。
    运行前请根据环境设置 `db_url` 并准备好 TAPD 配置。
    """
    from .config import load_tapd_config

    cfg = load_tapd_config()
    db_url = "mysql+pymysql://team_seek:seek#<EMAIL>:3306/team_seek"
    logger.warning("请根据本地数据库设置修改 db_url 后再运行 demo_run_sync_by_iteration_ids()")
    sample_ids = ["1148986738001001961", "1148986738001001948"]
    run_sync_by_iteration_ids(db_url=db_url, iteration_ids=sample_ids)


if __name__ == "__main__":
    from tapd_insight.config import setup_tapd_logging
    setup_tapd_logging("INFO")  # 想看响应体就改为 "DEBUG"
    # demo_run_sync()
    demo_run_sync_by_iteration_ids()


