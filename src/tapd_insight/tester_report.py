"""
测试人员团队报表生成模块。

功能：
- 按测试人员与时间范围抓取需求
- 计算团队与个人的工作负载与质量指标
- 生成 Markdown 报表
- 导出 Excel 明细（每位测试一个工作表）

依赖：pandas、openpyxl 用于 Excel 导出。
"""

from __future__ import annotations

from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta
import os

import pandas as pd

from .auth import TapdClientCredentialsAuth
from .story import TapdStoryClient, Story, StoryBugStats


logger = logging.getLogger("tapd_insight.tester_report")

@dataclass
class TeamOverviewMetrics:
    """团队总览指标。"""

    total_stories: int
    total_size_points: float
    total_valid_bugs: int
    total_invalid_bugs: int
    fat_bugs: int
    uat_bugs: int
    prod_verify_bugs: int
    prod_bugs: int
    invalid_rate_percent: Optional[float]
    uat_escape_rate_percent: Optional[float]
    prod_escape_rate_percent: Optional[float]


@dataclass
class PersonalMetrics:
    """个人指标，与团队指标口径一致。"""

    tester: str
    total_stories: int
    total_size_points: float
    total_valid_bugs: int
    total_invalid_bugs: int
    fat_bugs: int
    uat_bugs: int
    prod_verify_bugs: int
    prod_bugs: int
    invalid_rate_percent: Optional[float]
    uat_escape_rate_percent: Optional[float]
    prod_escape_rate_percent: Optional[float]


def _safe_float(val: Optional[str]) -> float:
    """将字符串转换为 float，失败返回 0.0。

    Story.size 为字符串，需要在统计时转换为数值。
    """
    if val is None:
        return 0.0
    try:
        return float(str(val))
    except Exception:
        return 0.0


def _sum_bug_stats(stats_list: List[StoryBugStats]) -> StoryBugStats:
    """聚合多个 StoryBugStats。

    仅累加字段，不处理 bugs、fixer_aggregates、reporter_counts。
    """
    total = StoryBugStats()
    for s in stats_list:
        total.fat_bugs += s.fat_bugs
        total.uat_bugs += s.uat_bugs
        total.prod_bugs += s.prod_bugs
        total.prod_verify_bugs += s.prod_verify_bugs
        total.valid_bugs += s.valid_bugs
        total.invalid_bugs += getattr(s, "invalid_bugs", 0)
    return total


def _calc_rates(valid_bugs: int, invalid_bugs: int, fat_bugs: int, uat_bugs: int, prod_verify_bugs: int, prod_bugs: int) -> Tuple[Optional[float], Optional[float], Optional[float]]:
    """计算无效率、UAT逃逸率、生产逃逸率。"""
    total_bugs = valid_bugs + invalid_bugs
    invalid_rate = None
    if total_bugs > 0:
        invalid_rate = float(invalid_bugs) / float(total_bugs) * 100.0

    uat_den = uat_bugs + fat_bugs
    uat_escape_rate = None
    if uat_den > 0:
        uat_escape_rate = float(uat_bugs) / float(uat_den) * 100.0

    prod_den = valid_bugs
    prod_escape_rate = None
    if prod_den > 0:
        prod_escape_rate = float(prod_verify_bugs + prod_bugs) / float(prod_den) * 100.0

    return invalid_rate, uat_escape_rate, prod_escape_rate


class TesterReportGenerator:
    """测试团队报表生成器。"""

    def __init__(self, auth: TapdClientCredentialsAuth) -> None:
        self._auth = auth
        self._story_client = TapdStoryClient(auth)

    def _fetch_stories_for_tester(
        self,
        *,
        workspace_id: str | int,
        tester: str,
        start_date: Optional[str],
        end_date: Optional[str],
    ) -> List[Story]:
        """按测试人员与时间窗口查询 Story 列表，端侧强制过滤与提前停止翻页，包含缺陷统计。"""
        from .time_utils import (
            filter_items_by_time_field,
            compute_min_datetime_in_items,
            parse_datetime_bound,
        )

        logger = logging.getLogger("tapd_insight.tester_report")

        # 服务端条件（尽量收敛，但不依赖其正确性）
        query_params: Dict[str, str] = {}
        if start_date or end_date:
            if start_date and end_date:
                query_params["created"] = f">={start_date} <{end_date}"
            elif start_date:
                query_params["created"] = f">={start_date}"
            elif end_date:
                query_params["created"] = f"<{end_date}"

        page = 1
        limit = 200
        all_stories: List[Story] = []
        start_dt = parse_datetime_bound(start_date, default_to_start=True) if start_date else None

        while True:
            page_items = self._story_client.get_story_by_tester_as_object(
                workspace_id=workspace_id,
                tester=tester,
                limit=limit,
                page=page,
                params=query_params or None,
                include_bug_stats=True,
                include_life_times=False,
            )
            if not page_items:
                break

            filtered, violations = filter_items_by_time_field(
                page_items,
                field_name="created",
                start_text=start_date,
                end_text=end_date,
            )
            if violations:
                logger.warning(f"tester_report 端侧越界过滤: {violations} 条 (tester={tester}, page={page})")
            all_stories.extend(filtered)

            # 提前停止：若当前页最小 created 早于下界，则不再继续
            min_cd = compute_min_datetime_in_items(page_items, field_name="created")
            if start_dt is not None and min_cd is not None and min_cd < start_dt:
                break
            if len(page_items) < limit:
                break
            page += 1

        return all_stories

    def _fetch_children_with_stats(self, *, workspace_id: str | int, child_ids: List[str]) -> List[Story]:
        """批量查询子需求，包含缺陷统计。"""
        if not child_ids:
            return []
        # TAPD 支持按逗号分隔的 id 批量查询
        return self._story_client.get_stories_as_objects(
            workspace_id=workspace_id,
            limit=len(child_ids),
            page=1,
            params={"id": ",".join(child_ids)},
            include_bug_stats=True,
            include_life_times=False,
        )

    def _aggregate_story_and_children_stats(self, *, workspace_id: str | int, story: Story) -> StoryBugStats:
        """聚合单个需求及其直接子需求的缺陷统计。"""
        base_stats = story.bug_stats or StoryBugStats()
        child_ids = story.children_id or []
        children: List[Story] = []
        if child_ids:
            children = self._fetch_children_with_stats(workspace_id=workspace_id, child_ids=child_ids)
        child_stats = [c.bug_stats or StoryBugStats() for c in children]
        total_stats = _sum_bug_stats([base_stats] + child_stats)
        return total_stats

    def _collect_team_top_level(self, stories: List[Story]) -> List[Story]:
        """过滤顶层需求，去重。"""
        tops: Dict[str, Story] = {}
        for s in stories:
            if not s.has_parent():
                tops[s.id] = s
        return list(tops.values())

    def _collect_unique_by_id(self, stories: List[Story]) -> List[Story]:
        """按 id 去重，保留首个。"""
        uniq: Dict[str, Story] = {}
        for s in stories:
            if s.id not in uniq:
                uniq[s.id] = s
        return list(uniq.values())

    def _compute_team_overview(
        self,
        *,
        workspace_id: str | int,
        testers_to_stories: Dict[str, List[Story]],
    ) -> TeamOverviewMetrics:
        """计算团队总览指标。"""
        # 汇总去重后的所有需求
        all_stories: List[Story] = []
        for stories in testers_to_stories.values():
            all_stories.extend(stories)
        all_stories = self._collect_unique_by_id(all_stories)

        top_stories = self._collect_team_top_level(all_stories)
        total_stories = len(top_stories)
        total_size = sum(_safe_float(s.size) for s in top_stories)

        # 缺陷指标需要包含需求与子需求
        agg_stats = StoryBugStats()
        for s in top_stories:
            s_stats = self._aggregate_story_and_children_stats(workspace_id=workspace_id, story=s)
            agg_stats = _sum_bug_stats([agg_stats, s_stats])

        invalid_rate, uat_escape_rate, prod_escape_rate = _calc_rates(
            agg_stats.valid_bugs,
            agg_stats.invalid_bugs,
            agg_stats.fat_bugs,
            agg_stats.uat_bugs,
            agg_stats.prod_verify_bugs,
            agg_stats.prod_bugs,
        )

        return TeamOverviewMetrics(
            total_stories=total_stories,
            total_size_points=total_size,
            total_valid_bugs=agg_stats.valid_bugs,
            total_invalid_bugs=agg_stats.invalid_bugs,
            fat_bugs=agg_stats.fat_bugs,
            uat_bugs=agg_stats.uat_bugs,
            prod_verify_bugs=agg_stats.prod_verify_bugs,
            prod_bugs=agg_stats.prod_bugs,
            invalid_rate_percent=invalid_rate,
            uat_escape_rate_percent=uat_escape_rate,
            prod_escape_rate_percent=prod_escape_rate,
        )

    def _compute_personal_metrics(
        self,
        *,
        workspace_id: str | int,
        tester: str,
        stories: List[Story],
    ) -> PersonalMetrics:
        """计算个人指标，口径与团队一致。"""
        # 顶层需求
        top_stories = self._collect_team_top_level(stories)

        # 需求数与规模需要包含子需求
        total_stories = 0
        total_size = 0.0
        for s in top_stories:
            children = self._fetch_children_with_stats(workspace_id=workspace_id, child_ids=s.children_id or [])
            total_stories += 1 + len(children)
            total_size += _safe_float(s.size)
            for c in children:
                total_size += _safe_float(c.size)

        # 缺陷指标包含需求与子需求
        agg_stats = StoryBugStats()
        for s in top_stories:
            s_stats = self._aggregate_story_and_children_stats(workspace_id=workspace_id, story=s)
            agg_stats = _sum_bug_stats([agg_stats, s_stats])

        invalid_rate, uat_escape_rate, prod_escape_rate = _calc_rates(
            agg_stats.valid_bugs,
            agg_stats.invalid_bugs,
            agg_stats.fat_bugs,
            agg_stats.uat_bugs,
            agg_stats.prod_verify_bugs,
            agg_stats.prod_bugs,
        )

        return PersonalMetrics(
            tester=tester,
            total_stories=total_stories,
            total_size_points=total_size,
            total_valid_bugs=agg_stats.valid_bugs,
            total_invalid_bugs=agg_stats.invalid_bugs,
            fat_bugs=agg_stats.fat_bugs,
            uat_bugs=agg_stats.uat_bugs,
            prod_verify_bugs=agg_stats.prod_verify_bugs,
            prod_bugs=agg_stats.prod_bugs,
            invalid_rate_percent=invalid_rate,
            uat_escape_rate_percent=uat_escape_rate,
            prod_escape_rate_percent=prod_escape_rate,
        )

    def _build_excel_for_person(
        self,
        *,
        workspace_id: str | int,
        tester: str,
        stories: List[Story],
    ) -> pd.DataFrame:
        """构建个人明细 DataFrame，按顶层需求聚合包含子需求的缺陷指标。"""
        rows: List[Dict[str, object]] = []
        top_stories = self._collect_team_top_level(stories)
        for s in top_stories:
            stats = self._aggregate_story_and_children_stats(workspace_id=workspace_id, story=s)
            invalid_rate, uat_escape_rate, prod_escape_rate = _calc_rates(
                stats.valid_bugs,
                stats.invalid_bugs,
                stats.fat_bugs,
                stats.uat_bugs,
                stats.prod_verify_bugs,
                stats.prod_bugs,
            )
            rows.append({
                "StoryID": s.id,
                "Name": s.name or "",
                "Created": s.created or "",
                "Size": _safe_float(s.size),
                "HasChildren": bool(s.children_id),
                "ValidBugs": stats.valid_bugs,
                "InvalidBugs": stats.invalid_bugs,
                "FAT": stats.fat_bugs,
                "UAT": stats.uat_bugs,
                "PROD_VERIFY": stats.prod_verify_bugs,
                "PROD": stats.prod_bugs,
                "InvalidRate%": round(invalid_rate, 2) if invalid_rate is not None else None,
                "UAT_Escape%": round(uat_escape_rate, 2) if uat_escape_rate is not None else None,
                "PROD_Escape%": round(prod_escape_rate, 2) if prod_escape_rate is not None else None,
            })
        df = pd.DataFrame(rows)
        return df

    def generate_report(
        self,
        *,
        workspace_id: str | int,
        testers: List[str],
        start_date: Optional[str],
        end_date: Optional[str],
        output_dir: Optional[str] = None,
    ) -> Tuple[str, str]:
        """生成 Markdown 报表与 Excel 明细。

        返回值为 (markdown_path, excel_path)。
        """
        output_dir = output_dir or os.path.join(os.getcwd(), "reports")
        os.makedirs(output_dir, exist_ok=True)

        testers_to_stories: Dict[str, List[Story]] = {}
        for tester in testers:
            stories = self._fetch_stories_for_tester(
                workspace_id=workspace_id,
                tester=tester,
                start_date=start_date,
                end_date=end_date,
            )
            testers_to_stories[tester] = stories

        team_metrics = self._compute_team_overview(
            workspace_id=workspace_id,
            testers_to_stories=testers_to_stories,
        )

        personal_metrics: List[PersonalMetrics] = []
        for tester, stories in testers_to_stories.items():
            pm = self._compute_personal_metrics(
                workspace_id=workspace_id,
                tester=tester,
                stories=stories,
            )
            personal_metrics.append(pm)

        markdown_path = self._write_markdown(
            testers=testers,
            start_date=start_date,
            end_date=end_date,
            team=team_metrics,
            personals=personal_metrics,
            output_dir=output_dir,
        )

        excel_path = self._write_excel(
            workspace_id=workspace_id,
            testers_to_stories=testers_to_stories,
            output_dir=output_dir,
        )

        return markdown_path, excel_path

    def _write_markdown(
        self,
        *,
        testers: List[str],
        start_date: Optional[str],
        end_date: Optional[str],
        team: TeamOverviewMetrics,
        personals: List[PersonalMetrics],
        output_dir: str,
    ) -> str:
        """生成 Markdown 报表文件。"""
        now_str = datetime.now().strftime("%Y%m%d_%H%M%S")
        fname = f"tester_report_{now_str}.md"
        fpath = os.path.join(output_dir, fname)

        def fmt(v: Optional[float]) -> str:
            if v is None:
                return "-"
            return f"{v:.2f}%"

        lines: List[str] = []
        lines.append(f"# 测试团队报表")
        lines.append("")
        lines.append(f"时间范围：{start_date or '-'} 至 {end_date or '-'}")
        lines.append(f"测试人员：{', '.join(testers)}")
        lines.append("")

        lines.append("## 一、团队总览指标")
        lines.append("")
        lines.append(f"- 总需求数（不含子需求）：{team.total_stories}")
        lines.append(f"- 团队总规模（不含子需求）：{team.total_size_points:.2f}")
        lines.append(f"- 缺陷分布（有效）：FAT={team.fat_bugs}，UAT={team.uat_bugs}，生产验收={team.prod_verify_bugs}，生产={team.prod_bugs}")
        lines.append(f"- 团队无效缺陷率：{fmt(team.invalid_rate_percent)}")
        lines.append(f"- 团队UAT缺陷逃逸率：{fmt(team.uat_escape_rate_percent)}")
        lines.append(f"- 团队总生产缺陷逃逸率：{fmt(team.prod_escape_rate_percent)}")
        lines.append("")

        lines.append("## 二、个人指标")
        lines.append("")
        # 表头：测试人员 | 需求数(含子) | 总规模(含子) | FAT | UAT | 生产验收 | 生产 | 无效缺陷率 | UAT逃逸率 | 总生产逃逸率
        lines.append("| 测试人员 | 需求数(含子) | 总规模(含子) | FAT | UAT | 生产验收 | 生产 | 无效缺陷率 | UAT缺陷逃逸率 | 总生产缺陷逃逸率 |")
        lines.append("| --- | ---: | ---: | ---: | ---: | ---: | ---: | ---: | ---: | ---: |")
        for pm in personals:
            lines.append(
                "| "
                + f"{pm.tester} | {pm.total_stories} | {pm.total_size_points:.2f} | "
                + f"{pm.fat_bugs} | {pm.uat_bugs} | {pm.prod_verify_bugs} | {pm.prod_bugs} | "
                + f"{fmt(pm.invalid_rate_percent)} | {fmt(pm.uat_escape_rate_percent)} | {fmt(pm.prod_escape_rate_percent)} |"
            )
        lines.append("")

        with open(fpath, "w", encoding="utf-8") as f:
            f.write("\n".join(lines))

        return fpath

    def _write_excel(
        self,
        *,
        workspace_id: str | int,
        testers_to_stories: Dict[str, List[Story]],
        output_dir: str,
    ) -> str:
        """为每位测试导出一张明细表。"""
        now_str = datetime.now().strftime("%Y%m%d_%H%M%S")
        fname = f"tester_report_{now_str}.xlsx"
        fpath = os.path.join(output_dir, fname)

        with pd.ExcelWriter(fpath, engine="openpyxl") as writer:
            for tester, stories in testers_to_stories.items():
                df = self._build_excel_for_person(
                    workspace_id=workspace_id,
                    tester=tester,
                    stories=stories,
                )
                # Excel Sheet 名称长度有限制，必要时截断
                sheet_name = tester[:31] if tester else "Sheet"
                df.to_excel(writer, index=False, sheet_name=sheet_name)

        return fpath


def demo_generate_recent_3_months() -> None:
    """演示：输出指定两位测试近3个月报表。"""
    from .config import load_tapd_config

    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(client_id=cfg.client_id, client_secret=cfg.client_secret)
    gen = TesterReportGenerator(auth)

    testers = ["陈辉龙", "陈广发", "张雯慧", "周佩佩", "毛浩", "陈赞旭", "谢杰明"]
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=90)).strftime("%Y-%m-%d")

    md_path, xlsx_path = gen.generate_report(
        workspace_id=cfg.workspace_id,
        testers=testers,
        start_date=start_date,
        end_date=end_date,
        output_dir=os.path.join(os.getcwd(), "reports"),
    )
    logger.info(f"Markdown 报表：{md_path}")
    logger.info(f"Excel 明细：{xlsx_path}")


if __name__ == "__main__":
    demo_generate_recent_3_months()


