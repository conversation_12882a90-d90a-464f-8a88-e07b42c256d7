"""tapd_insight 包入口与懒加载导出。

避免在包导入时提前导入子模块，防止使用 `python -m tapd_insight.story` 时出现运行时警告。
"""

from typing import TYPE_CHECKING, Any

__all__ = [
    "TapdClientCredentialsAuth",
    "TapdAuthError",
    "AccessToken",
    "TapdStoryClient",
    "TapdApiError",
    "StoryRelatedBug",
    "Story",
    "TapdBugClient",
    "Bug",
    "TapdIterationClient",
    "TapdReportClient",
    "DefectEscapeResult",
    "IterationDefectEscapeResult",
    "TapdMeasureClient",
    "LifeTime",
]

if TYPE_CHECKING:
    from .auth import TapdClientCredentialsAuth, TapdAuthError, AccessToken
    from .story import TapdStoryClient, TapdApiError
    from .report import TapdReportClient, DefectEscapeResult, IterationDefectEscapeResult
    from .measure import TapdMeasureClient, LifeTime


def __getattr__(name: str) -> Any:
    """按需懒加载导出符号，避免包导入时预加载子模块。"""
    if name in {"TapdClientCredentialsAuth", "TapdAuthError", "AccessToken"}:
        from . import auth as _auth
        return getattr(_auth, name)
    if name in {"TapdStoryClient", "TapdApiError"}:
        from . import story as _story
        return getattr(_story, name)
    if name == "StoryRelatedBug":
        from . import story as _story
        return getattr(_story, name)
    if name == "Story":
        from . import story as _story
        return getattr(_story, name)
    if name == "TapdBugClient":
        from . import bug as _bug
        return getattr(_bug, name)
    if name == "Bug":
        from . import bug as _bug
        return getattr(_bug, name)
    if name == "TapdIterationClient":
        from . import iteration as _iteration
        return getattr(_iteration, name)
    if name in {"TapdReportClient", "DefectEscapeResult", "IterationDefectEscapeResult"}:
        from . import report as _report
        return getattr(_report, name)
    if name in {"TapdMeasureClient", "LifeTime"}:
        from . import measure as _measure
        return getattr(_measure, name)
    raise AttributeError(f"module 'tapd_insight' has no attribute {name!r}")


def main() -> None:
    print("Hello from tapd-insight!")
