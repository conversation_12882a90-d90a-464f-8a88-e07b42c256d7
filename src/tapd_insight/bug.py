"""
TAPD 缺陷管理：查询缺陷列表与按 ID 查询缺陷。

参考文档：
https://open.tapd.cn/document/api-doc/API%E6%96%87%E6%A1%A3/api_reference/bug/get_bugs.html

使用标准库实现 HTTP 请求，依赖 `TapdClientCredentialsAuth` 提供的 Bearer Token。
"""

from __future__ import annotations

import json
from typing import Any, Dict, List, Optional
import logging
from dataclasses import dataclass
from urllib import parse
import requests

from .auth import TapdClientCredentialsAuth
from .story import TapdApiError
from .transport import TapdApiTransport


DEFAULT_BUGS_URL = "https://api.tapd.cn/bugs"
DEFAULT_BUG_RELATED_STORIES_URL = "https://api.tapd.cn/bugs/get_related_stories"


@dataclass
class Bug:
    """TAPD 缺陷领域对象，保留常用关键字段。"""

    id: str
    title: Optional[str]
    priority: Optional[str]
    priority_label: Optional[str]  # 优先级标签
    severity: Optional[str]  # 严重等级 prompt...
    status: Optional[str]  # 状态 closed...
    story_id: Optional[str] # 关联需求id, 可能为空
    iteration_id: Optional[str]  # 迭代id
    current_owner: Optional[str]  # 当前处理人
    reporter: Optional[str]  # 缺陷报告人
    created: Optional[str]  # 创建时间
    de: Optional[str]  # 开发人员
    te: Optional[str]  # 测试人员
    bugtype: Optional[str]  # 缺陷类型
    fixer: Optional[str]  # 修复人，也标识缺陷的开发人员
    closer: Optional[str]  # 关闭人
    workspace_id: str
    env: Optional[str]  # 环境
    flows: Optional[str]  # 缺陷状态历经流程 new|rejected|closed
    deadline: Optional[str]  # 截止时间

    # 复开信息
    has_reopened: bool = False  # 是否曾被复开
    reopen_times: int = 0  # 复开次数
    def is_invalid(self) -> bool:
        """判断缺陷是否为无效缺陷。

        无效缺陷的判断标准：
        1. 缺陷状态是 closed
        2. flows 字段中历经流程的最后两个状态是 rejected|closed

        这表示缺陷被拒绝了，然后关闭的。

        Returns:
            bool: 如果是无效缺陷返回 True，否则返回 False
        """
        # 检查状态是否为 closed
        if self.status != "closed":
            return False

        # 检查 flows 字段
        if not self.flows:
            return False

        # 解析流程，按 | 分割
        flow_steps = self.flows.split("|")

        # 检查流程是否至少有两个步骤
        if len(flow_steps) < 2:
            return False

        # 检查最后两个步骤是否为 rejected 和 closed
        last_two_steps = flow_steps[-2:]
        return last_two_steps == ["rejected", "closed"]

    def get_environment(self) -> Optional[str]:
        """获取缺陷所属的环境。

        从 env 字段解析环境类型，支持以下四种环境：
        - FAT环境 -> "FAT"
        - 生产环境 -> "PROD"
        - UAT环境 -> "UAT"
        - 生产验证环境 -> "PROD_VERIFY"

        若 env 为空或不匹配，返回 None。
        """
        if not self.env:
            return None

        env_lower = self.env.strip().lower()
        if "fat" in env_lower:
            return "FAT"
        elif "生产验证" in self.env:
            return "PROD_VERIFY"
        elif "生产" in self.env:
            return "PROD"
        elif "uat" in env_lower:
            return "UAT"
        else:
            return None

    def get_status_display(self) -> str:
        """获取缺陷状态的中文显示名称。

        将英文状态码转换为对应的中文描述：
        - new -> "新缺陷"
        - unconfirmed -> "修复方案评审"
        - feedback -> "修复方案评审不通过"
        - in_progress -> "修复中"
        - resolved -> "待测试"
        - planning -> "测试中"
        - status_1 -> "待UAT"
        - status_2 -> "UAT完成"
        - status_3 -> "已投产"
        - rejected -> "已拒绝"
        - reopened -> "重新打开"
        - closed -> "已关闭"
        - suspended -> "挂起"

        若状态为空或不匹配，返回原状态值。
        """
        if not self.status:
            return ""

        status_mapping = {
            "new": "新缺陷",
            "unconfirmed": "修复方案评审",
            "feedback": "修复方案评审不通过",
            "in_progress": "修复中",
            "resolved": "待测试",
            "planning": "测试中",
            "status_1": "待UAT",
            "status_2": "UAT完成",
            "status_3": "已投产",
            "rejected": "已拒绝",
            "reopened": "重新打开",
            "closed": "已关闭",
            "suspended": "挂起"
        }

        return status_mapping.get(self.status, self.status)

    @staticmethod
    def from_api(bug_dict: Dict[str, Any]) -> "Bug":
        """从 API 返回的单条 Bug 字典构建领域对象。"""
        def to_str(val: Any) -> Optional[str]:
            if val is None:
                return None
            return str(val)

        bug_id = to_str(bug_dict.get("id")) or ""
        workspace_id = to_str(bug_dict.get("workspace_id")) or ""
        # 解析复开次数：flows 形如 "new|resolved|reopened|..."，支持 reopen/reopened 两种记号
        flows_val = to_str(bug_dict.get("flows")) or ""
        tokens = [t.strip().lower() for t in flows_val.split("|") if t.strip()]
        reopen_count = sum(1 for t in tokens if t in {"reopen", "reopened"})
        has_reopen_flag = reopen_count > 0

        return Bug(
            id=bug_id,
            title=to_str(bug_dict.get("title")),
            priority=to_str(bug_dict.get("priority")),
            priority_label=to_str(bug_dict.get("priority_label")),
            severity=to_str(bug_dict.get("severity")),
            status=to_str(bug_dict.get("status")),
            story_id=to_str(bug_dict.get("story_id")),
            iteration_id=to_str(bug_dict.get("iteration_id")),
            current_owner=to_str(bug_dict.get("current_owner")),
            reporter=to_str(bug_dict.get("reporter")),
            created=to_str(bug_dict.get("created")),
            de=to_str(bug_dict.get("de")),
            te=to_str(bug_dict.get("te")),
            bugtype=to_str(bug_dict.get("bugtype")),
            fixer=to_str(bug_dict.get("fixer")),
            closer=to_str(bug_dict.get("closer")),
            workspace_id=workspace_id,
            env=to_str(bug_dict.get("custom_field_one")),
            flows=flows_val,
            deadline=to_str(bug_dict.get("deadline")),
            has_reopened=has_reopen_flag,
            reopen_times=reopen_count,
        )


@dataclass
class BugRelatedStory:
    """缺陷关联需求关系实体。"""

    workspace_id: str
    bug_id: str
    story_id: str


class TapdBugClient:
    """TAPD 缺陷客户端，提供查询相关能力。"""

    def __init__(self, auth: TapdClientCredentialsAuth, *, bugs_url: str = DEFAULT_BUGS_URL, request_timeout_seconds: float = 10.0) -> None:
        """初始化缺陷客户端。"""
        self._auth = auth
        self._bugs_url = bugs_url
        self._timeout = request_timeout_seconds
        self._transport = TapdApiTransport(request_timeout_seconds=request_timeout_seconds)
        self._related_stories_url = DEFAULT_BUG_RELATED_STORIES_URL

    def get_bugs(
        self,
        *,
        workspace_id: str | int,
        fields: Optional[str] = None,
        limit: Optional[int] = None,
        page: Optional[int] = None,
        order: Optional[str] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """查询缺陷列表。

        仅做一次请求，不做自动翻页。对返回结构进行扁平化，提取每项中的 `Bug` 字段。
        """
        query: Dict[str, Any] = {}
        query.update(params or {})
        query["workspace_id"] = str(workspace_id)

        if fields:
            query["fields"] = fields
        if limit is not None:
            query["limit"] = int(limit)
        if page is not None:
            query["page"] = int(page)
        if order:
            query["order"] = order

        qs = parse.urlencode(query, doseq=True)
        url = f"{self._bugs_url}?{qs}" if qs else self._bugs_url

        headers = {
            "Authorization": self._auth.get_authorization_header(),
            "Accept": "application/json",
        }

        logger = logging.getLogger("tapd_insight.bug")
        logger.info(f"GET {url}")
        logger.debug(f"Headers: {self._mask_sensitive_headers(headers)}")

        try:
            resp = self._transport.request("GET", url, headers=headers)
            status_code = resp.status_code
            payload = resp.json()
        except requests.exceptions.HTTPError as e:
            details = None
            try:
                details = resp.text[:500]
            except Exception:
                details = None
            raise TapdApiError(
                f"HTTP {status_code} 调用 TAPD bugs 接口失败",
                status_code=status_code,
                payload={"details": details},
            ) from e
        except requests.exceptions.RequestException as e:
            logging.getLogger("tapd_insight.bug").error("无法连接 TAPD bugs 接口")
            raise TapdApiError("无法连接 TAPD bugs 接口", payload={"reason": str(e)}) from e

        if not isinstance(payload, dict) or payload.get("status") != 1:
            raise TapdApiError("TAPD bugs 接口返回业务状态异常", status_code=status_code, payload=payload)

        data = payload.get("data")
        if not isinstance(data, list):
            return []

        bugs: List[Dict[str, Any]] = []
        for item in data:
            if isinstance(item, dict) and "Bug" in item and isinstance(item["Bug"], dict):
                bugs.append(item["Bug"])
        return bugs

    def get_bug_by_id(self, *, workspace_id: str | int, bug_id: str | int, fields: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """按 ID 查询单个缺陷，未命中返回 None。"""
        result = self.get_bugs(
            workspace_id=workspace_id,
            fields=fields,
            limit=1,
            params={"id": str(bug_id)},
        )
        return result[0] if result else None

    @staticmethod
    def _mask_sensitive_headers(headers: Dict[str, str]) -> Dict[str, str]:
        """脱敏处理敏感请求头信息。"""
        masked = headers.copy()
        if "Authorization" in masked:
            auth_value = masked["Authorization"]
            if auth_value.startswith("Bearer "):
                token = auth_value[7:]  # 移除 "Bearer " 前缀
                if len(token) > 8:
                    masked["Authorization"] = f"Bearer {token[:4]}***{token[-4:]}"
                else:
                    masked["Authorization"] = "Bearer ***MASKED***"
        return masked

    def get_bugs_as_objects(
        self,
        *,
        workspace_id: str | int,
        fields: Optional[str] = None,
        limit: Optional[int] = None,
        page: Optional[int] = None,
        order: Optional[str] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> List[Bug]:
        """查询缺陷列表并返回 `Bug` 对象列表。"""
        raw_list = self.get_bugs(
            workspace_id=workspace_id,
            fields=fields,
            limit=limit,
            page=page,
            order=order,
            params=params,
        )
        return [Bug.from_api(item) for item in raw_list]

    def get_related_stories(
        self,
        *,
        workspace_id: str | int,
        bug_id: str | int | List[str | int],
    ) -> List[BugRelatedStory]:
        """获取缺陷关联的需求ID，支持单个或多个 bug_id。

        参考：GET https://api.tapd.cn/bugs/get_related_stories
        文档说明支持多ID查询（逗号分隔）。
        """
        # 兼容单个与多个 bug_id
        if isinstance(bug_id, (list, tuple, set)):
            ids: List[str] = [str(x) for x in bug_id if str(x).strip()]
            bug_id_value = ",".join(ids)
        else:
            bug_id_value = str(bug_id)

        query: Dict[str, Any] = {
            "workspace_id": str(workspace_id),
            "bug_id": bug_id_value,
        }
        qs = parse.urlencode(query, doseq=True)
        url = f"{self._related_stories_url}?{qs}"

        headers = {
            "Authorization": self._auth.get_authorization_header(),
            "Accept": "application/json",
        }

        logger = logging.getLogger("tapd_insight.bug")
        logger.info(f"GET {url}")
        logger.debug(f"Headers: {self._mask_sensitive_headers(headers)}")

        try:
            resp = self._transport.request("GET", url, headers=headers)
            status_code = resp.status_code
            payload = resp.json()
            logger.info(f"响应 {status_code} GET {url}")
            try:
                logger.debug(f"Response JSON: {resp.text}")
            except Exception:
                logger.debug("Response JSON: <unavailable>")
        except requests.exceptions.HTTPError as e:
            details = None
            try:
                details = resp.text[:500]
            except Exception:
                details = None
            raise TapdApiError(
                f"HTTP {status_code} 调用 TAPD bugs/get_related_stories 接口失败",
                status_code=status_code,
                payload={"details": details},
            ) from e
        except requests.exceptions.RequestException as e:
            logging.getLogger("tapd_insight.bug").error("无法连接 TAPD bugs/get_related_stories 接口")
            raise TapdApiError("无法连接 TAPD bugs/get_related_stories 接口", payload={"reason": str(e)}) from e

        if not isinstance(payload, dict) or payload.get("status") != 1:
            raise TapdApiError("TAPD bugs/get_related_stories 接口返回业务状态异常", status_code=status_code, payload=payload)

        data = payload.get("data")
        if not isinstance(data, list):
            return []

        relations: List[BugRelatedStory] = []
        for item in data:
            if not isinstance(item, dict):
                continue
            workspace_id_val = item.get("workspace_id")
            bug_id_val = item.get("bug_id")
            story_id_val = item.get("story_id")
            if workspace_id_val is None or bug_id_val is None or story_id_val is None:
                continue
            relations.append(
                BugRelatedStory(
                    workspace_id=str(workspace_id_val),
                    bug_id=str(bug_id_val),
                    story_id=str(story_id_val),
                )
            )
        return relations

    def get_bug_by_id_as_object(self, *, workspace_id: str | int, bug_id: str | int, fields: Optional[str] = None) -> Optional[Bug]:
        """按 ID 查询单个缺陷并返回 `Bug` 对象。未命中返回 None。"""
        raw = self.get_bug_by_id(workspace_id=workspace_id, bug_id=bug_id, fields=fields)
        return Bug.from_api(raw) if raw else None

    def get_bugs_by_reporter(
        self,
        *,
        workspace_id: str | int,
        reporter: str,
        created_start: Optional[str] = None,
        created_end: Optional[str] = None,
        fields: Optional[str] = None,
        limit: Optional[int] = None,
        page: Optional[int] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> List[Bug]:
        """根据缺陷报告人和创建时间范围查询缺陷列表。"""
        from .time_utils import filter_items_by_time_field, compute_min_datetime_in_items
        logger = logging.getLogger("tapd_insight.bug")
        query_params = params.copy() if params else {}
        query_params["reporter"] = reporter

        if created_start or created_end:
            if created_start and created_end:
                query_params["created"] = f">={created_start} <{created_end}"
            elif created_start:
                query_params["created"] = f">={created_start}"
            elif created_end:
                query_params["created"] = f"<{created_end}"

        all_results: List[Bug] = []
        page_no = page or 1
        page_size = limit or 200
        while True:
            page_items = self.get_bugs_as_objects(
                workspace_id=workspace_id,
                fields=fields,
                limit=page_size,
                page=page_no,
                order="created desc",
                params=query_params,
            )
            if not page_items:
                break
            filtered, violations = filter_items_by_time_field(
                page_items,
                field_name="created",
                start_text=created_start,
                end_text=created_end,
            )
            if violations:
                logger.warning(f"bugs_by_reporter 端侧越界过滤: {violations} 条 (page={page_no})")
            all_results.extend(filtered)

            min_dt = compute_min_datetime_in_items(page_items, field_name="created")
            from .time_utils import parse_datetime_bound
            start_dt = parse_datetime_bound(created_start, default_to_start=True) if created_start else None
            if start_dt is not None and min_dt is not None and min_dt < start_dt:
                break
            if len(page_items) < page_size:
                break
            page_no += 1
        return all_results

    def get_bugs_by_fixer(
        self,
        *,
        workspace_id: str | int,
        fixer: str,
        created_start: Optional[str] = None,
        created_end: Optional[str] = None,
        fields: Optional[str] = None,
        limit: Optional[int] = None,
        page: Optional[int] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> List[Bug]:
        """根据缺陷修复人和创建时间范围查询缺陷列表。"""
        from .time_utils import filter_items_by_time_field, compute_min_datetime_in_items, parse_datetime_bound
        logger = logging.getLogger("tapd_insight.bug")
        query_params = params.copy() if params else {}
        query_params["fixer"] = fixer

        if created_start or created_end:
            if created_start and created_end:
                query_params["created"] = f">={created_start} <{created_end}"
            elif created_start:
                query_params["created"] = f">={created_start}"
            elif created_end:
                query_params["created"] = f"<{created_end}"

        all_results: List[Bug] = []
        page_no = page or 1
        page_size = limit or 200
        while True:
            page_items = self.get_bugs_as_objects(
                workspace_id=workspace_id,
                fields=fields,
                limit=page_size,
                page=page_no,
                order="created desc",
                params=query_params,
            )
            if not page_items:
                break
            filtered, violations = filter_items_by_time_field(
                page_items,
                field_name="created",
                start_text=created_start,
                end_text=created_end,
            )
            if violations:
                logger.warning(f"bugs_by_fixer 端侧越界过滤: {violations} 条 (page={page_no})")
            all_results.extend(filtered)

            min_dt = compute_min_datetime_in_items(page_items, field_name="created")
            start_dt = parse_datetime_bound(created_start, default_to_start=True) if created_start else None
            if start_dt is not None and min_dt is not None and min_dt < start_dt:
                break
            if len(page_items) < page_size:
                break
            page_no += 1
        return all_results

    def get_bugs_by_created_range(
        self,
        *,
        workspace_id: str | int,
        created_start: Optional[str] = None,
        created_end: Optional[str] = None,
        fields: Optional[str] = None,
        limit: Optional[int] = None,
        page: Optional[int] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> List[Bug]:
        """根据创建时间范围查询缺陷列表，按创建时间降序。"""
        from .time_utils import filter_items_by_time_field, compute_min_datetime_in_items, parse_datetime_bound
        logger = logging.getLogger("tapd_insight.bug")
        query_params = params.copy() if params else {}
        if created_start and created_end:
            query_params["created"] = f">={created_start} <{created_end}"
        elif created_start:
            query_params["created"] = f">={created_start}"
        elif created_end:
            query_params["created"] = f"<{created_end}"

        all_results: List[Bug] = []
        page_no = page or 1
        page_size = limit or 200
        while True:
            page_items = self.get_bugs_as_objects(
                workspace_id=workspace_id,
                fields=fields,
                limit=page_size,
                page=page_no,
                order="created desc",
                params=query_params,
            )
            if not page_items:
                break
            filtered, violations = filter_items_by_time_field(
                page_items,
                field_name="created",
                start_text=created_start,
                end_text=created_end,
            )
            if violations:
                logger.warning(f"bugs_by_created_range 端侧越界过滤: {violations} 条 (page={page_no})")
            all_results.extend(filtered)

            min_dt = compute_min_datetime_in_items(page_items, field_name="created")
            start_dt = parse_datetime_bound(created_start, default_to_start=True) if created_start else None
            if start_dt is not None and min_dt is not None and min_dt < start_dt:
                break
            if len(page_items) < page_size:
                break
            page_no += 1
        return all_results

    def _get_bugs_by_ids(
        self,
        *,
        workspace_id: str | int,
        bug_ids: List[str | int],
        fields: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        按多个缺陷 ID 批量查询缺陷（一次或分批多次请求，聚合返回）。

        依据官方文档，`id` 支持多 ID 查询，且每次请求最大 limit=200。
        会对传入 ID 做去重与清洗，并按 200 一批拆分查询。
        """
        if not bug_ids:
            return []
        # 去重并保持顺序
        seen: set[str] = set()
        cleaned: List[str] = []
        for bid in bug_ids:
            s = str(bid).strip()
            if not s or s in seen:
                continue
            seen.add(s)
            cleaned.append(s)
        if not cleaned:
            return []

        all_bugs: List[Dict[str, Any]] = []
        batch_size = 200
        for i in range(0, len(cleaned), batch_size):
            chunk = cleaned[i:i + batch_size]
            try:
                page_bugs = self.get_bugs(
                    workspace_id=workspace_id,
                    fields=fields,
                    limit=min(len(chunk), batch_size),
                    page=1,
                    params={"id": ",".join(chunk)},
                )
            except Exception:
                logging.getLogger("tapd_insight.bug").exception(
                    f"get_bugs_by_ids failed on chunk {i//batch_size+1} size={len(chunk)}"
                )
                page_bugs = []
            if page_bugs:
                all_bugs.extend(page_bugs)
        return all_bugs

    def get_bugs_by_ids_as_objects(
        self,
        *,
        workspace_id: str | int,
        bug_ids: List[str | int],
        fields: Optional[str] = None,
    ) -> List[Bug]:
        """
        按多个缺陷 ID 批量查询并返回 Bug 对象列表。
        """
        raws = self._get_bugs_by_ids(workspace_id=workspace_id, bug_ids=bug_ids, fields=fields)
        return [Bug.from_api(item) for item in raws]

def demo_get_bugs_by_ids_as_objects() -> None:
    """Demo：演示按多个缺陷 ID 批量查询并返回 Bug 对象列表。"""
    from .config import load_tapd_config
    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(client_id=cfg.client_id, client_secret=cfg.client_secret)
    client = TapdBugClient(auth)
    bug_ids = ["1148986738001033452", "1148986738001033451"]
    bugs = client.get_bugs_by_ids_as_objects(workspace_id=cfg.workspace_id, bug_ids=bug_ids)
    logging.getLogger("tapd_insight.bug").info(f"demo_get_bugs_by_ids_as_objects: bug_ids={bug_ids}, bugs={len(bugs)}, bugs={bugs}")

def demo_get_related_stories() -> None:
    """Demo：演示缺陷关联的需求ID查询，包含单个与多个 bug_id 两种用法。"""
    from .config import load_tapd_config
    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(client_id=cfg.client_id, client_secret=cfg.client_secret)
    client = TapdBugClient(auth)

    # 示例：单个 bug_id
    sample_bug = "1148986738001033340"
    rels_single = client.get_related_stories(workspace_id=cfg.workspace_id, bug_id=sample_bug)
    logging.getLogger("tapd_insight.bug").info(f"demo_get_related_stories(single): bug_id={sample_bug}, relations={len(rels_single)}")
    for r in rels_single[:5]:
        logging.getLogger("tapd_insight.bug").info(f"  workspace={r.workspace_id} bug={r.bug_id} -> story={r.story_id}")

    # 示例：多个 bug_id
    bug_ids = [sample_bug, "1148986738001033316"]
    rels_multi = client.get_related_stories(workspace_id=cfg.workspace_id, bug_id=bug_ids)
    logging.getLogger("tapd_insight.bug").info(f"demo_get_related_stories(multi): bug_ids={bug_ids}, relations={len(rels_multi)}")
    # 统计每个 bug 的关联 story 数量
    counts: Dict[str, int] = {}
    for r in rels_multi:
        counts[r.bug_id] = counts.get(r.bug_id, 0) + 1
    for bid in bug_ids:
        logging.getLogger("tapd_insight.bug").info(f"  bug_id={bid} -> story_count={counts.get(bid, 0)}")


if __name__ == "__main__":
    from tapd_insight.config import setup_tapd_logging
    setup_tapd_logging("INFO")  # 想看响应体就改为 "DEBUG"

    # Demo: 缺陷关联的需求
    # demo_get_related_stories()
    demo_get_bugs_by_ids_as_objects()
