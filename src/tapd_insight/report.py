"""
TAPD 报表分析：提供各种统计分析功能。

包含需求生产缺陷逃逸率等质量度量指标的计算。
"""

from __future__ import annotations

from typing import Dict, List, Optional
import logging
from dataclasses import dataclass

from .auth import TapdClientCredentialsAuth
from .story import TapdStoryClient
from .bug import TapdBugClient
from .iteration import TapdIterationClient


@dataclass
class DefectEscapeResult:
    """需求生产缺陷逃逸率计算结果。"""

    story_id: str
    story_name: Optional[str]
    test_lead: Optional[str]
    tech_lead: Optional[str]
    total_bugs: int
    fat_bugs: int
    uat_bugs: int
    prod_bugs: int
    prod_verify_bugs: int
    production_bugs: int  # PROD + PROD_VERIFY（根据 include_prod_verify 参数）
    escape_rate: float
    size: Optional[str]  # 需求规模

    def __str__(self) -> str:
        story_display = f"{self.story_name}({self.story_id})" if self.story_name else self.story_id
        size_display = f"，规模 {self.size}" if self.size else ""
        return (f"需求 {story_display}: 总缺陷 {self.total_bugs} "
                f"(FAT:{self.fat_bugs} UAT:{self.uat_bugs} PROD:{self.prod_bugs} PROD_VERIFY:{self.prod_verify_bugs})，"
                f"生产缺陷 {self.production_bugs}，逃逸率 {self.escape_rate:.2%}{size_display}")

    def get_environment_summary(self) -> str:
        """获取环境维度缺陷分布摘要。"""
        return (f"环境分布: FAT {self.fat_bugs}，UAT {self.uat_bugs}，"
                f"PROD {self.prod_bugs}，PROD_VERIFY {self.prod_verify_bugs}")

    @property
    def all_production_bugs(self) -> int:
        """所有生产环境缺陷（PROD + PROD_VERIFY）。"""
        return self.prod_bugs + self.prod_verify_bugs

    def get_lead_escape_summary(self) -> str:
        """输出测试/研发负责人维度的逃逸率（对单需求即为本需求逃逸率）。"""
        lead_rate = f"{self.escape_rate:.2%}" if self.total_bugs > 0 else "0.00%"
        parts = []
        if self.test_lead:
            parts.append(f"测试负责人 {self.test_lead}: {lead_rate}")
        if self.tech_lead:
            parts.append(f"研发负责人 {self.tech_lead}: {lead_rate}")
        return "；".join(parts) if parts else "无负责人信息"


@dataclass
class LeadEscapeSummary:
    """按负责人维度的逃逸率聚合统计。"""
    story_count: int
    total_bugs: int
    production_bugs: int
    escape_rate: float
    total_size: int  # 总规模（所有需求规模的累计）


@dataclass
class TesterDefectEscapeResult:
    """测试人员缺陷逃逸率计算结果，包含整体统计和每个需求的详细信息。"""

    tester: str
    overall_summary: LeadEscapeSummary
    story_results: List[DefectEscapeResult]

    def __str__(self) -> str:
        return (f"测试人员 {self.tester}: 负责需求数 {self.overall_summary.story_count}，"
                f"总规模 {self.overall_summary.total_size}，"
                f"总缺陷 {self.overall_summary.total_bugs}，"
                f"生产缺陷 {self.overall_summary.production_bugs}，"
                f"整体逃逸率 {self.overall_summary.escape_rate:.2%}")

    def get_story_escape_summary(self) -> str:
        """获取需求维度逃逸率汇总信息。"""
        if not self.story_results:
            return "无需求数据"

        lines = [f"需求维度逃逸率 ({len(self.story_results)} 个需求):"]
        for result in self.story_results:
            lines.append(f"  - {result}")
        return "\n".join(lines)


@dataclass
class IterationDefectEscapeResult:
    """迭代生产缺陷逃逸率计算结果。"""

    iteration_id: str
    iteration_name: Optional[str]
    total_stories: int
    total_size: int  # 总规模（迭代下所有需求规模的累计）
    total_bugs: int
    fat_bugs: int
    uat_bugs: int
    prod_bugs: int
    prod_verify_bugs: int
    production_bugs: int  # PROD + PROD_VERIFY（根据 include_prod_verify 参数）
    overall_escape_rate: float
    story_results: List[DefectEscapeResult]
    test_lead_stats: Dict[str, LeadEscapeSummary]
    tech_lead_stats: Dict[str, LeadEscapeSummary]

    def __str__(self) -> str:
        iteration_display = self.iteration_name or self.iteration_id
        return (f"迭代 {iteration_display}: 需求数 {self.total_stories}，总规模 {self.total_size}，总缺陷 {self.total_bugs} "
                f"(FAT:{self.fat_bugs} UAT:{self.uat_bugs} PROD:{self.prod_bugs} PROD_VERIFY:{self.prod_verify_bugs})，"
                f"生产缺陷 {self.production_bugs}，整体逃逸率 {self.overall_escape_rate:.2%}")

    def get_story_escape_summary(self) -> str:
        """获取需求维度逃逸率汇总信息。"""
        if not self.story_results:
            return "无需求数据"

        lines = [f"需求维度逃逸率 ({len(self.story_results)} 个需求):"]
        for result in self.story_results:
            lines.append(f"  - {result}")
        return "\n".join(lines)

    def get_environment_distribution(self) -> str:
        """获取整体环境缺陷分布信息。"""
        return (f"迭代环境分布: FAT {self.fat_bugs}，UAT {self.uat_bugs}，"
                f"PROD {self.prod_bugs}，PROD_VERIFY {self.prod_verify_bugs}")

    @property
    def all_production_bugs(self) -> int:
        """所有生产环境缺陷（PROD + PROD_VERIFY）。"""
        return self.prod_bugs + self.prod_verify_bugs

    def get_test_lead_escape_summary(self) -> str:
        """按测试负责人输出逃逸率统计。"""
        if not self.test_lead_stats:
            return "无测试负责人统计"
        lines = ["测试负责人逃逸率:"]
        for name, stat in sorted(self.test_lead_stats.items(), key=lambda kv: kv[0] or ""):
            lines.append(
                f"  - {name}: 需求数 {stat.story_count}，总规模 {stat.total_size}，总缺陷 {stat.total_bugs}，"
                f"生产缺陷 {stat.production_bugs}，逃逸率 {stat.escape_rate:.2%}"
            )
        return "\n".join(lines)

    def get_tech_lead_escape_summary(self) -> str:
        """按研发负责人输出逃逸率统计。"""
        if not self.tech_lead_stats:
            return "无研发负责人统计"
        lines = ["研发负责人逃逸率:"]
        for name, stat in sorted(self.tech_lead_stats.items(), key=lambda kv: kv[0] or ""):
            lines.append(
                f"  - {name}: 需求数 {stat.story_count}，总规模 {stat.total_size}，总缺陷 {stat.total_bugs}，"
                f"生产缺陷 {stat.production_bugs}，逃逸率 {stat.escape_rate:.2%}"
            )
        return "\n".join(lines)


class TapdReportClient:
    """TAPD 报表分析客户端。"""

    def __init__(self, auth: TapdClientCredentialsAuth) -> None:
        """初始化报表客户端。"""
        self._story_client = TapdStoryClient(auth)
        self._bug_client = TapdBugClient(auth)
        self._iteration_client = TapdIterationClient(auth)

    def calculate_story_defect_escape_rate(
        self,
        *,
        workspace_id: str | int,
        story_id: str | int,
        bug_fields: Optional[str] = None,
        include_prod_verify: bool = True
    ) -> DefectEscapeResult:
        """计算单个需求的生产缺陷逃逸率。

        逃逸率 = 生产环境发现的缺陷数量 / 总缺陷数量

        参数:
            workspace_id: 项目 ID
            story_id: 需求 ID
            bug_fields: 查询缺陷时的字段列表，默认包含环境判断所需字段
            include_prod_verify: 是否将 PROD_VERIFY 也计入生产缺陷，默认 True

        返回:
            DefectEscapeResult 包含逃逸率计算结果
        """
        # 获取需求基本信息（使用对象化，便于读取 leads）
        story_obj = self._story_client.get_story_by_id_as_object(
            workspace_id=workspace_id,
            story_id=story_id,
            fields="id,name,size,custom_field_one,custom_field_six,developer"
        )
        story_name = story_obj.name if story_obj else None
        story_size = story_obj.size if story_obj else None
        test_lead = story_obj.test_lead if story_obj else None
        tech_lead = story_obj.tech_lead if story_obj else None

        # 获取需求关联的缺陷 ID 列表
        related_bugs = self._story_client.get_related_bugs(
            workspace_id=workspace_id,
            story_id=story_id
        )

        if not related_bugs:
            return DefectEscapeResult(
                story_id=str(story_id),
                story_name=story_name,
                total_bugs=0,
                fat_bugs=0,
                uat_bugs=0,
                prod_bugs=0,
                prod_verify_bugs=0,
                production_bugs=0,
                escape_rate=0.0,
                test_lead=test_lead,
                tech_lead=tech_lead,
                size=story_size,
            )

        # 提取缺陷 ID 列表
        bug_ids = [relation.bug_id for relation in related_bugs]

        # 批量查询缺陷详情
        if bug_fields is None:
            bug_fields = "id,custom_field_one,workspace_id"

        bugs = self._bug_client.get_bugs_as_objects(
            workspace_id=workspace_id,
            fields=bug_fields,
            params={"id": ",".join(bug_ids)}
        )

        # 统计各环境缺陷数量
        total_bugs = len(bugs)
        fat_bugs = 0
        uat_bugs = 0
        prod_bugs = 0
        prod_verify_bugs = 0

        for bug in bugs:
            environment = bug.get_environment()
            if environment == "PROD":
                prod_bugs += 1
            elif environment == "PROD_VERIFY":
                prod_verify_bugs += 1
            elif environment == "UAT":
                uat_bugs += 1
            elif environment == "FAT":
                fat_bugs += 1
            else:
                # 没有环境标识的归类到 FAT 环境
                fat_bugs += 1

        # 计算生产缺陷数量和逃逸率
        production_bugs = prod_bugs + (prod_verify_bugs if include_prod_verify else 0)
        escape_rate = production_bugs / total_bugs if total_bugs > 0 else 0.0

        return DefectEscapeResult(
            story_id=str(story_id),
            story_name=story_name,
            test_lead=test_lead,
            tech_lead=tech_lead,
            total_bugs=total_bugs,
            fat_bugs=fat_bugs,
            uat_bugs=uat_bugs,
            prod_bugs=prod_bugs,
            prod_verify_bugs=prod_verify_bugs,
            production_bugs=production_bugs,
            escape_rate=escape_rate,
            size=story_size
        )

    def calculate_multiple_stories_defect_escape_rate(
        self,
        *,
        workspace_id: str | int,
        story_ids: List[str | int],
        bug_fields: Optional[str] = None,
        include_prod_verify: bool = True
    ) -> List[DefectEscapeResult]:
        """批量计算多个需求的生产缺陷逃逸率。

        参数:
            workspace_id: 项目 ID
            story_ids: 需求 ID 列表
            bug_fields: 查询缺陷时的字段列表
            include_prod_verify: 是否将 PROD_VERIFY 也计入生产缺陷，默认 True

        返回:
            DefectEscapeResult 列表
        """
        results = []
        for story_id in story_ids:
            try:
                result = self.calculate_story_defect_escape_rate(
                    workspace_id=workspace_id,
                    story_id=story_id,
                    bug_fields=bug_fields,
                    include_prod_verify=include_prod_verify
                )
                results.append(result)
            except Exception as e:
                # 记录错误但继续处理其他需求
                logging.getLogger("tapd_insight.report").error(f"计算需求 {story_id} 逃逸率时出错: {e}")
                continue

        return results

    def calculate_iteration_defect_escape_rate(
        self,
        *,
        workspace_id: str | int,
        iteration_id: str | int,
        bug_fields: Optional[str] = None,
        include_prod_verify: bool = True
    ) -> IterationDefectEscapeResult:
        """计算某个迭代下所有需求的生产缺陷逃逸率。

        提供整体逃逸率和需求维度的逃逸率分析。

        参数:
            workspace_id: 项目 ID
            iteration_id: 迭代 ID
            bug_fields: 查询缺陷时的字段列表
            include_prod_verify: 是否将 PROD_VERIFY 也计入生产缺陷，默认 True

        返回:
            IterationDefectEscapeResult 包含迭代整体和需求维度的逃逸率
        """
        # 获取迭代基本信息
        iteration_info = self._iteration_client.get_iteration_by_id(
            workspace_id=workspace_id,
            iteration_id=iteration_id,
            fields="id,name"
        )
        iteration_name = iteration_info.get("name") if iteration_info else None

        # 获取迭代下的所有需求
        stories = self._story_client.get_stories_as_objects(
            workspace_id=workspace_id,
            fields="id",
            params={"iteration_id": str(iteration_id)}
        )

        if not stories:
            return IterationDefectEscapeResult(
                iteration_id=str(iteration_id),
                iteration_name=iteration_name,
                total_stories=0,
                total_size=0,
                total_bugs=0,
                fat_bugs=0,
                uat_bugs=0,
                prod_bugs=0,
                prod_verify_bugs=0,
                production_bugs=0,
                overall_escape_rate=0.0,
                story_results=[],
                test_lead_stats={},
                tech_lead_stats={},
            )

        # 批量计算各需求的逃逸率
        story_ids = [story.id for story in stories]
        story_results = self.calculate_multiple_stories_defect_escape_rate(
            workspace_id=workspace_id,
            story_ids=story_ids,
            bug_fields=bug_fields,
            include_prod_verify=include_prod_verify
        )

        # 计算整体统计
        total_stories = len(story_results)
        total_size = 0
        for result in story_results:
            if result.size and result.size.isdigit():
                total_size += int(result.size)
        total_bugs = sum(result.total_bugs for result in story_results)
        fat_bugs = sum(result.fat_bugs for result in story_results)
        uat_bugs = sum(result.uat_bugs for result in story_results)
        prod_bugs = sum(result.prod_bugs for result in story_results)
        prod_verify_bugs = sum(result.prod_verify_bugs for result in story_results)
        production_bugs = sum(result.production_bugs for result in story_results)
        overall_escape_rate = production_bugs / total_bugs if total_bugs > 0 else 0.0

        # 负责人维度聚合
        test_lead_stats: Dict[str, LeadEscapeSummary] = {}
        tech_lead_stats: Dict[str, LeadEscapeSummary] = {}

        def add_stat(bucket: Dict[str, LeadEscapeSummary], key: Optional[str], item: DefectEscapeResult) -> None:
            if not key:
                return
            stat = bucket.get(key)
            if stat is None:
                stat = LeadEscapeSummary(story_count=0, total_bugs=0, production_bugs=0, escape_rate=0.0, total_size=0)
                bucket[key] = stat
            stat.story_count += 1
            stat.total_bugs += item.total_bugs
            stat.production_bugs += item.production_bugs
            # 累计规模
            if item.size and item.size.isdigit():
                stat.total_size += int(item.size)

        for r in story_results:
            add_stat(test_lead_stats, r.test_lead, r)
            add_stat(tech_lead_stats, r.tech_lead, r)

        for bucket in (test_lead_stats, tech_lead_stats):
            for key, stat in bucket.items():
                stat.escape_rate = (stat.production_bugs / stat.total_bugs) if stat.total_bugs > 0 else 0.0

        return IterationDefectEscapeResult(
            iteration_id=str(iteration_id),
            iteration_name=iteration_name,
            total_stories=total_stories,
            total_size=total_size,
            total_bugs=total_bugs,
            fat_bugs=fat_bugs,
            uat_bugs=uat_bugs,
            prod_bugs=prod_bugs,
            prod_verify_bugs=prod_verify_bugs,
            production_bugs=production_bugs,
            overall_escape_rate=overall_escape_rate,
            story_results=story_results,
            test_lead_stats=test_lead_stats,
            tech_lead_stats=tech_lead_stats,
        )

    def calculate_tester_defect_escape_rate(
        self,
        *,
        workspace_id: str | int,
        tester: str,
        bug_fields: Optional[str] = None,
        include_prod_verify: bool = True,
        limit: Optional[int] = None,
        created_start: Optional[str] = None,
        created_end: Optional[str] = None
    ) -> TesterDefectEscapeResult:
        """计算某个测试人员负责的所有需求的缺陷逃逸率。

        参数:
            workspace_id: 项目 ID
            tester: 测试人员姓名
            bug_fields: 查询缺陷时的字段列表
            include_prod_verify: 是否将 PROD_VERIFY 也计入生产缺陷，默认 True
            limit: 查询需求的数量限制，如果不设置可能查询到所有相关需求
            created_start: 需求创建开始时间，格式如 "2025-12-01"
            created_end: 需求创建截止时间，格式如 "2025-12-31"

        返回:
            TesterDefectEscapeResult 包含该测试人员的整体逃逸率统计和每个需求的详细信息
        """
        from .time_utils import filter_items_by_time_field
        logger = logging.getLogger("tapd_insight.report")

        # 获取该测试人员负责的需求（一次性拉取或按 limit）
        stories = self._story_client.get_story_by_tester_as_object(
            workspace_id=workspace_id,
            tester=tester,
            fields="id,name,created",
            limit=limit,
            params=None,
        )

        # 端侧时间过滤
        if created_start or created_end:
            stories, violations = filter_items_by_time_field(
                stories,
                field_name="created",
                start_text=created_start,
                end_text=created_end,
            )
            if violations:
                logger.warning(f"tester_report 端侧越界过滤: {violations} 条 (tester={tester})")

        if not stories:
            return TesterDefectEscapeResult(
                tester=tester,
                overall_summary=LeadEscapeSummary(
                    story_count=0,
                    total_bugs=0,
                    production_bugs=0,
                    escape_rate=0.0,
                    total_size=0
                ),
                story_results=[]
            )

        # 提取需求 ID 列表
        story_ids = [story.id for story in stories]

        # 批量计算各需求的逃逸率
        story_results = self.calculate_multiple_stories_defect_escape_rate(
            workspace_id=workspace_id,
            story_ids=story_ids,
            bug_fields=bug_fields,
            include_prod_verify=include_prod_verify
        )

        # 聚合统计
        total_bugs = sum(result.total_bugs for result in story_results)
        production_bugs = sum(result.production_bugs for result in story_results)
        escape_rate = production_bugs / total_bugs if total_bugs > 0 else 0.0

        # 计算总规模
        total_size = 0
        for result in story_results:
            if result.size and result.size.isdigit():
                total_size += int(result.size)

        overall_summary = LeadEscapeSummary(
            story_count=len(story_results),
            total_bugs=total_bugs,
            production_bugs=production_bugs,
            escape_rate=escape_rate,
            total_size=total_size
        )

        return TesterDefectEscapeResult(
            tester=tester,
            overall_summary=overall_summary,
            story_results=story_results
        )


if __name__ == "__main__":
    # 直接从同包内导入，避免再次触发包级懒加载逻辑
    from .auth import TapdClientCredentialsAuth
    from .config import load_tapd_config

    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(
        client_id=cfg.client_id,
        client_secret=cfg.client_secret,
    )

    report_client = TapdReportClient(auth)

    # # # 示例：批量计算多个需求的逃逸率
    # story_ids = ["1148986738001058391", "1148986738001061818"]  # 示例 ID
    # results = report_client.calculate_multiple_stories_defect_escape_rate(
    #     workspace_id=cfg.workspace_id,
    #     story_ids=story_ids,
    #     include_prod_verify=True
    # )

    # print("\n各需求逃逸率:")
    # for result in results:
    #     print(f"  {result}")  # 已包含规模显示
    #     # 输出该需求的负责人维度逃逸率
    #     print(f"    - {result.get_lead_escape_summary()}")

    # # 示例：计算测试人员"张雯慧"在指定时间范围内的缺陷逃逸率（包含每个需求的详细信息）
    # tester_name = "张雯慧"
    # tester_escape_result = report_client.calculate_tester_defect_escape_rate(
    #     workspace_id=cfg.workspace_id,
    #     tester=tester_name,
    #     include_prod_verify=True,
    #     limit=100,  # 限制查询数量，避免查询过多需求
    #     created_start="2025-07-01",  # 查询2025年7月1日之后创建的需求
    #     created_end="2025-12-31"     # 查询2025年12月31日之前创建的需求
    # )

    # print(f"\n{tester_escape_result}")
    # print(f"时间范围: 2025-07-01 ~ 2025-12-31")
    # print(f"详细统计:")
    # print(f"  - 负责需求数: {tester_escape_result.overall_summary.story_count}")
    # print(f"  - 总规模: {tester_escape_result.overall_summary.total_size}")
    # print(f"  - 总缺陷数: {tester_escape_result.overall_summary.total_bugs}")
    # print(f"  - 生产缺陷数: {tester_escape_result.overall_summary.production_bugs}")
    # print(f"  - 整体逃逸率: {tester_escape_result.overall_summary.escape_rate:.2%}")

    # # 输出每个需求的详细逃逸率
    # print(f"\n{tester_escape_result.get_story_escape_summary()}")

    # # 示例：团队测试人员5个月内缺陷逃逸率统计
    # print("\n" + "="*80)
    # print("团队测试人员5个月内缺陷逃逸率统计")
    # print("="*80)

    # # 计算5个月的时间范围（当前时间向前推5个月）
    # from datetime import datetime, timedelta
    # import calendar

    # # 获取当前日期
    # today = datetime.now()

    # # 计算5个月前的日期
    # # 向前推5个月：先减去5个月，再调整到月初
    # if today.month > 5:
    #     five_months_ago = today.replace(month=today.month - 5, day=1)
    # else:
    #     # 跨年情况
    #     five_months_ago = today.replace(year=today.year - 1, month=today.month + 12 - 5, day=1)

    # # 格式化时间范围
    # start_date = five_months_ago.strftime("%Y-%m-%d")
    # end_date = today.strftime("%Y-%m-%d")

    # print(f"统计时间范围: {start_date} ~ {end_date}")
    # print()

    # # 团队测试人员列表
    # team_testers = ["张雯慧", "陈辉龙", "陈广发", "毛浩", "周佩佩", "陈赞旭", "谢杰明"]

    # # 存储所有测试人员的结果
    # team_results = {}

    # for tester in team_testers:
    #     print(f"正在统计测试人员: {tester}")
    #     try:
    #         result = report_client.calculate_tester_defect_escape_rate(
    #             workspace_id=cfg.workspace_id,
    #             tester=tester,
    #             include_prod_verify=True,
    #             limit=200,  # 适当增加限制，避免遗漏
    #             created_start=start_date,
    #             created_end=end_date
    #         )
    #         team_results[tester] = result
    #         print(f"  ✓ 完成")
    #     except Exception as e:
    #         print(f"  ✗ 统计失败: {e}")
    #         team_results[tester] = None

    # print()
    # print("="*80)
    # print("团队测试人员缺陷逃逸率汇总")
    # print("="*80)

    # # 输出汇总表格
    # print(f"{'测试人员':<10} {'需求数':<8} {'总规模':<8} {'总缺陷':<8} {'生产缺陷':<10} {'逃逸率':<10}")
    # print("-" * 70)

    # # 团队总计
    # total_stories = 0
    # total_size = 0
    # total_bugs = 0
    # total_production_bugs = 0

    # for tester in team_testers:
    #     result = team_results.get(tester)
    #     if result:
    #         summary = result.overall_summary
    #         print(f"{tester:<10} {summary.story_count:<8} {summary.total_size:<8} "
    #               f"{summary.total_bugs:<8} {summary.production_bugs:<10} "
    #               f"{summary.escape_rate:<10.2%}")

    #         # 累计团队总数
    #         total_stories += summary.story_count
    #         total_size += summary.total_size
    #         total_bugs += summary.total_bugs
    #         total_production_bugs += summary.production_bugs
    #     else:
    #         print(f"{tester:<10} {'N/A':<8} {'N/A':<8} {'N/A':<8} {'N/A':<10} {'N/A':<10}")

    # print("-" * 70)

    # # 计算团队整体逃逸率
    # team_escape_rate = total_production_bugs / total_bugs if total_bugs > 0 else 0.0
    # print(f"{'团队总计':<10} {total_stories:<8} {total_size:<8} "
    #       f"{total_bugs:<8} {total_production_bugs:<10} "
    #       f"{team_escape_rate:<10.2%}")

    # print()
    # print("详细分析:")
    # print(f"  - 团队总需求数: {total_stories}")
    # print(f"  - 团队总规模: {total_size}")
    # print(f"  - 团队总缺陷数: {total_bugs}")
    # print(f"  - 团队生产缺陷数: {total_production_bugs}")
    # print(f"  - 团队整体逃逸率: {team_escape_rate:.2%}")

    # # 输出排名（按逃逸率从低到高）
    # print()
    # print("逃逸率排名（从优到差）:")
    # valid_results = [(tester, result) for tester, result in team_results.items() if result]
    # valid_results.sort(key=lambda x: x[1].overall_summary.escape_rate)

    # for i, (tester, result) in enumerate(valid_results, 1):
    #     summary = result.overall_summary
    #     print(f"  {i}. {tester}: {summary.escape_rate:.2%} "
    #           f"(需求数: {summary.story_count}, 总规模: {summary.total_size}, 生产缺陷: {summary.production_bugs}/{summary.total_bugs})")

    # 示例：计算迭代维度的逃逸率
    iteration_id = "1148986738001001948"  # 示例迭代 ID
    iteration_result = report_client.calculate_iteration_defect_escape_rate(
        workspace_id=cfg.workspace_id,
        iteration_id=iteration_id,
        include_prod_verify=True
    )

    logging.getLogger("tapd_insight.report").info(f"\n迭代整体逃逸率: {iteration_result}")
    logging.getLogger("tapd_insight.report").info(f"\n{iteration_result.get_environment_distribution()}")
    logging.getLogger("tapd_insight.report").info(f"\n{iteration_result.get_story_escape_summary()}")
    # 输出迭代维度的测试/研发负责人逃逸率统计（包含各负责人的总规模）
    logging.getLogger("tapd_insight.report").info(f"\n{iteration_result.get_test_lead_escape_summary()}")
    logging.getLogger("tapd_insight.report").info(f"\n{iteration_result.get_tech_lead_escape_summary()}")