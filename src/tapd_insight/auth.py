"""
TAPD OpenAPI client_credentials 授权获取与刷新 access token。

参考文档：
https://open.tapd.cn/document/api-doc/API%E6%96%87%E6%A1%A3/%E6%8E%88%E6%9D%83%E5%87%AD%E8%AF%81/%E9%A1%B9%E7%9B%AE%E6%80%81.html

使用 requests 与统一传输层处理 HTTP 调用与重试。
"""

from __future__ import annotations

import base64
import threading
import time
from dataclasses import dataclass
import logging
from typing import Any, Dict, Optional

import requests
from .transport import TapdApiTransport


DEFAULT_TOKEN_URL = "https://api.tapd.cn/tokens/request_token"


class TapdAuthError(Exception):
    """TAPD 授权相关错误。

    捕获 HTTP 层错误、解析错误以及业务状态错误等场景。
    """

    def __init__(self, message: str, *, status_code: Optional[int] = None, payload: Optional[Dict[str, Any]] = None) -> None:
        super().__init__(message)
        self.status_code = status_code
        self.payload = payload


@dataclass
class AccessToken:
    """内存中的 access token 表示与过期管理信息。"""

    access_token: str
    token_type: str
    expires_in_seconds: int
    expiry_monotonic_deadline: float
    scope: Optional[str] = None
    resource: Optional[Dict[str, Any]] = None
    server_now: Optional[str] = None

    def is_expired(self, *, skew_seconds: int = 120) -> bool:
        """判断 token 是否过期。

        使用 monotonic 时钟判断，将 `skew_seconds` 作为提前刷新余量，避免并发或时钟偏差导致的边界错误。
        """
        now = time.monotonic()
        return now + skew_seconds >= self.expiry_monotonic_deadline

    def authorization_header_value(self) -> str:
        """返回可直接用于 HTTP Authorization 头的值。"""
        return f"{self.token_type} {self.access_token}"


class TapdClientCredentialsAuth:
    """使用 client_credentials 模式获取与缓存 TAPD access token 的轻量客户端。

    基于 TAPD 文档的授权流程，实现了按需刷新、并发安全与过期管理。
    """

    def __init__(
        self,
        *,
        client_id: str,
        client_secret: str,
        token_url: str = DEFAULT_TOKEN_URL,
        request_timeout_seconds: float = 10.0,
        expiry_skew_seconds: int = 120,
    ) -> None:
        """初始化授权客户端。

        参数说明：
        client_id 与 client_secret 为应用的凭据。
        token_url 默认指向 TAPD 文档提供的换取 token 接口。
        request_timeout_seconds 为请求超时秒数。
        expiry_skew_seconds 为判断过期的提前余量秒数。
        """
        if not client_id:
            raise ValueError("client_id 不能为空")
        if not client_secret:
            raise ValueError("client_secret 不能为空")

        self._client_id = client_id
        self._client_secret = client_secret
        self._token_url = token_url
        self._timeout = request_timeout_seconds
        self._expiry_skew_seconds = expiry_skew_seconds
        self._transport = TapdApiTransport(request_timeout_seconds=request_timeout_seconds)

        self._lock = threading.RLock()
        self._token: Optional[AccessToken] = None

    def get_access_token(self) -> str:
        """获取可用的 access token。

        若本地没有缓存或缓存已过期，则同步刷新。
        返回值仅为原始 token 字符串，若需要 Authorization 头值可使用 `get_authorization_header`。
        """
        token = self._get_or_refresh_token()
        return token.access_token

    def get_authorization_header(self) -> str:
        """返回可直接设置到 HTTP 头部 Authorization 的值。"""
        token = self._get_or_refresh_token()
        return token.authorization_header_value()

    def force_refresh(self) -> AccessToken:
        """强制刷新 token 并返回完整的 `AccessToken`。"""
        with self._lock:
            self._token = self._fetch_token()
            return self._token

    def _get_or_refresh_token(self) -> AccessToken:
        with self._lock:
            if self._token is None or self._token.is_expired(skew_seconds=self._expiry_skew_seconds):
                self._token = self._fetch_token()
            return self._token

    def _fetch_token(self) -> AccessToken:
        """执行一次网络请求，按 TAPD client_credentials 流程换取 access token。"""
        basic = self._encode_basic(self._client_id, self._client_secret)

        headers = {
            "Authorization": f"Basic {basic}",
            "Content-Type": "application/x-www-form-urlencoded",
            "Accept": "application/json",
        }

        form_data = {"grant_type": "client_credentials"}

        # 调试信息：使用标准日志，按要求区分级别
        logger = logging.getLogger("tapd_insight.auth")
        logger.info(f"POST {self._token_url}")
        logger.debug(f"Headers: {self._mask_sensitive_headers(headers)}")
        logger.debug("Body: grant_type=client_credentials")

        try:
            resp = self._transport.request(
                "POST",
                self._token_url,
                headers=headers,
                data=form_data,
            )
            status_code = resp.status_code
            payload = resp.json()
            # 响应日志：状态 info，payload debug
            logger.info(f"响应 {status_code} POST {self._token_url}")
            try:
                logger.debug(f"Response JSON: {resp.text}")
            except Exception:
                logger.debug("Response JSON: <unavailable>")
        except requests.exceptions.HTTPError as e:
            details = None
            try:
                details = resp.text[:500]
            except Exception:
                details = None
            raise TapdAuthError(
                f"HTTP {status_code} 调用 TAPD token 接口失败",
                status_code=status_code,
                payload={"details": details},
            ) from e
        except requests.exceptions.RequestException as e:
            raise TapdAuthError("无法连接 TAPD token 接口", payload={"reason": str(e)}) from e

        if not isinstance(payload, dict) or payload.get("status") != 1:
            raise TapdAuthError("TAPD token 接口返回业务状态异常", status_code=status_code, payload=payload)

        data = payload.get("data") or {}
        access_token = data.get("access_token")
        token_type = data.get("token_type", "Bearer")
        expires_in = data.get("expires_in")
        scope = data.get("scope")
        resource = data.get("resource")
        server_now = data.get("now")

        if not access_token or not isinstance(expires_in, int):
            raise TapdAuthError("TAPD token 返回缺少必要字段", status_code=status_code, payload=payload)

        deadline = time.monotonic() + max(0, int(expires_in))

        return AccessToken(
            access_token=access_token,
            token_type=token_type,
            expires_in_seconds=int(expires_in),
            expiry_monotonic_deadline=deadline,
            scope=scope,
            resource=resource if isinstance(resource, dict) else None,
            server_now=server_now if isinstance(server_now, str) else None,
        )

    @staticmethod
    def _encode_basic(client_id: str, client_secret: str) -> str:
        """按 HTTP Basic 规范对 `client_id:client_secret` 做 Base64 编码。"""
        token = f"{client_id}:{client_secret}".encode("utf-8")
        return base64.b64encode(token).decode("utf-8")

    @staticmethod
    def _mask_sensitive_headers(headers: Dict[str, str]) -> Dict[str, str]:
        """脱敏处理敏感请求头信息。"""
        masked = headers.copy()
        if "Authorization" in masked:
            auth_value = masked["Authorization"]
            if auth_value.startswith("Basic "):
                masked["Authorization"] = "Basic ***MASKED***"
            elif auth_value.startswith("Bearer "):
                token = auth_value[7:]  # 移除 "Bearer " 前缀
                if len(token) > 8:
                    masked["Authorization"] = f"Bearer {token[:4]}***{token[-4:]}"
                else:
                    masked["Authorization"] = "Bearer ***MASKED***"
        return masked


if __name__ == "__main__":
    from .config import load_tapd_config
    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(
        client_id=cfg.client_id,
        client_secret=cfg.client_secret
    )
    try:
        # 只要需要就会自动刷新
        access_token = auth.get_access_token()
        print("access_token:", access_token)

        # 若要直接设置到 HTTP 头部
        authorization = auth.get_authorization_header()
        print("Authorization:", authorization)

        # 强制刷新
        token_obj = auth.force_refresh()
        print("expires_in:", token_obj.expires_in_seconds)
    except TapdAuthError as e:
        print("获取 TAPD token 失败:", e, e.status_code, e.payload)
