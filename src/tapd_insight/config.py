"""
TAPD 连接配置管理。

推荐使用环境变量配置敏感信息，避免将凭据硬编码到代码库：
  - TAPD_CLIENT_ID
  - TAPD_CLIENT_SECRET

如需本地开发持久化，可在终端会话中设置，或借助 .env 工具加载到环境变量。
本模块仅使用标准库实现环境读取，避免强制引入第三方依赖。
"""

from __future__ import annotations

import os
from dataclasses import dataclass
import logging
from typing import Optional

# 默认工作空间 ID
DEFAULT_WORKSPACE_ID = "48986738"

@dataclass
class TapdConfig:
    """TAPD 访问配置。"""

    client_id: str
    client_secret: str
    workspace_id: str


def load_tapd_config(*, strict: bool = True) -> TapdConfig:
    """从环境变量加载 TAPD 配置。

    当 strict 为 True 且缺少 client_id 或 client_secret 时抛出 ValueError。
    """
    client_id = os.getenv("TAPD_CLIENT_ID", "").strip()
    client_secret = os.getenv("TAPD_CLIENT_SECRET", "").strip()
    workspace_id = os.getenv("TAPD_WORKSPACE_ID", DEFAULT_WORKSPACE_ID).strip()

    if strict and (not client_id or not client_secret):
        raise ValueError(
            "未检测到 TAPD_CLIENT_ID 或 TAPD_CLIENT_SECRET 环境变量，请在运行前进行设置"
        )

    return TapdConfig(
        client_id=client_id,
        client_secret=client_secret,
        workspace_id=workspace_id
    )


def setup_tapd_logging(level: str | None = None) -> None:
    """配置 tapd_insight 包的日志输出级别与格式。

    优先级顺序为函数入参 > 环境变量 TAPD_LOG_LEVEL > 默认 INFO。
    支持级别：ERROR、WARNING、INFO、DEBUG。
    """
    desired_level = (level or os.getenv("TAPD_LOG_LEVEL") or "INFO").upper().strip()
    numeric_level = getattr(logging, desired_level, logging.INFO)

    logger = logging.getLogger("tapd_insight")
    logger.setLevel(numeric_level)
    logger.propagate = False

    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            fmt="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)

