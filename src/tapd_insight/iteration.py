"""
TAPD 迭代管理：查询迭代列表与按 ID 查询迭代。

参考文档：
https://open.tapd.cn/document/api-doc/API%E6%96%87%E6%A1%A3/api_reference/iteration/get_iterations.html

使用标准库实现 HTTP 请求，依赖 `TapdClientCredentialsAuth` 提供的 Bearer Token。
"""

from __future__ import annotations

import json
import logging
from typing import Any, Dict, List, Optional
from urllib import parse
import requests

from .auth import TapdClientCredentialsAuth
from .story import TapdApiError, Story
from .transport import TapdApiTransport
from dataclasses import dataclass
logger = logging.getLogger("tapd_insight.iteration")



DEFAULT_ITERATIONS_URL = "https://api.tapd.cn/iterations"



@dataclass
class Iteration:
    """TAPD 迭代对象。"""

    id: str
    name: Optional[str]
    workspace_id: Optional[str]
    startdate: Optional[str]  # 开始日期
    enddate: Optional[str]  # 结束日期
    status: Optional[str]  # 状态
    creator: Optional[str]  # 创建人
    created: Optional[str]  # 创建时间
    modified: Optional[str]  # 修改时间
    completed: Optional[str]  # 完成时间
    description: Optional[str]  # 描述

    @staticmethod
    def from_dict(iteration_dict: Dict[str, Any]) -> "Iteration":
        """从 TAPD API 返回的字典构造 Iteration 对象。"""

        def to_str(value: Any) -> Optional[str]:
            """安全转换为字符串。"""
            if value is None:
                return None
            return str(value).strip() if str(value).strip() else None

        return Iteration(
            id=to_str(iteration_dict.get("id")) or "",
            name=to_str(iteration_dict.get("name")),
            workspace_id=to_str(iteration_dict.get("workspace_id")),
            startdate=to_str(iteration_dict.get("startdate")),
            enddate=to_str(iteration_dict.get("enddate")),
            status=to_str(iteration_dict.get("status")),
            creator=to_str(iteration_dict.get("creator")),
            created=to_str(iteration_dict.get("created")),
            modified=to_str(iteration_dict.get("modified")),
            completed=to_str(iteration_dict.get("completed")),
            description=to_str(iteration_dict.get("description")),
        )


class IterationStory(Story):
    """迭代视角的需求对象。

    继承自 `Story`，新增格式化输出方法 `toString`。
    """

    def toString(self) -> str:
        """输出必要元素：名称、ID、产品经理、创建时间、评审通过时间、状态、研发负责人、测试负责人。"""
        name = self.name or ""
        sid = self.id or ""
        pm_display = str(getattr(self, "product_manager", None) or "")
        created = self.created or ""
        review_passed = self.review_passed or ""
        status = self.status or ""
        tech_lead = self.tech_lead or ""
        test_lead = self.test_lead or ""

        return (
            f"名称: {name} | ID: {sid} | 产品经理: {pm_display} | 创建: {created} | "
            f"评审通过: {review_passed} | 状态: {status} | 研发负责人: {tech_lead} | 测试负责人: {test_lead}"
        )


class TapdIterationClient:
    """TAPD 迭代客户端，提供查询相关能力。"""

    def __init__(self, auth: TapdClientCredentialsAuth, *, iterations_url: str = DEFAULT_ITERATIONS_URL, request_timeout_seconds: float = 10.0) -> None:
        """初始化迭代客户端。"""
        self._auth = auth
        self._iterations_url = iterations_url
        self._timeout = request_timeout_seconds
        self._transport = TapdApiTransport(request_timeout_seconds=request_timeout_seconds)

    @staticmethod
    def _mask_sensitive_headers(headers: Dict[str, str]) -> Dict[str, str]:
        """脱敏处理敏感请求头信息。"""
        masked = headers.copy()
        if "Authorization" in masked:
            auth_value = masked["Authorization"]
            if auth_value.startswith("Bearer "):
                token = auth_value[7:]  # 移除 "Bearer " 前缀
                if len(token) > 8:
                    masked["Authorization"] = f"Bearer {token[:4]}***{token[-4:]}"
                else:
                    masked["Authorization"] = "Bearer ***MASKED***"
        return masked

    def get_iterations(
        self,
        *,
        workspace_id: str | int,
        fields: Optional[str] = None,
        limit: Optional[int] = None,
        page: Optional[int] = None,
        order: Optional[str] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """查询迭代列表。

        仅做一次请求，不做自动翻页。对返回结构进行扁平化，提取每项中的 `Iteration` 字段。
        """
        query: Dict[str, Any] = {}
        query.update(params or {})
        query["workspace_id"] = str(workspace_id)

        if fields:
            query["fields"] = fields
        if limit is not None:
            query["limit"] = int(limit)
        if page is not None:
            query["page"] = int(page)
        if order:
            query["order"] = order

        qs = parse.urlencode(query, doseq=True)
        url = f"{self._iterations_url}?{qs}" if qs else self._iterations_url

        headers = {
            "Authorization": self._auth.get_authorization_header(),
            "Accept": "application/json",
        }

        logger = logging.getLogger("tapd_insight.iteration")
        logger.info(f"GET {url}")
        logger.debug(f"Headers: {self._mask_sensitive_headers(headers)}")

        try:
            resp = self._transport.request("GET", url, headers=headers)
            status_code = resp.status_code
            payload = resp.json()
        except requests.exceptions.HTTPError as e:
            details = None
            try:
                details = resp.text[:500]
            except Exception:
                details = None
            raise TapdApiError(
                f"HTTP {status_code} 调用 TAPD iterations 接口失败",
                status_code=status_code,
                payload={"details": details},
            ) from e
        except requests.exceptions.RequestException as e:
            logging.getLogger("tapd_insight.iteration").error("无法连接 TAPD iterations 接口")
            raise TapdApiError("无法连接 TAPD iterations 接口", payload={"reason": str(e)}) from e

        if not isinstance(payload, dict) or payload.get("status") != 1:
            raise TapdApiError("TAPD iterations 接口返回业务状态异常", status_code=status_code, payload=payload)

        data = payload.get("data")
        if not isinstance(data, list):
            return []

        iterations: List[Dict[str, Any]] = []
        for item in data:
            if isinstance(item, dict) and "Iteration" in item and isinstance(item["Iteration"], dict):
                iterations.append(item["Iteration"])
        return iterations

    def get_iteration_by_id(self, *, workspace_id: str | int, iteration_id: str | int, fields: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """按 ID 查询单个迭代，未命中返回 None。"""
        result = self.get_iterations(
            workspace_id=workspace_id,
            fields=fields,
            limit=1,
            params={"id": str(iteration_id)},
        )
        return result[0] if result else None

    def get_iterations_by_date_range(
        self,
        *,
        workspace_id: str | int,
        start_date_from: Optional[str] = None,
        start_date_to: Optional[str] = None,
        end_date_from: Optional[str] = None,
        end_date_to: Optional[str] = None,
        fields: Optional[str] = None,
        limit: Optional[int] = None,
        page: Optional[int] = None,
        order: Optional[str] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """根据开始时间和结束时间范围查询迭代列表。

        参数:
            workspace_id: 项目 ID
            start_date_from: 开始时间的起始日期 (格式: YYYY-MM-DD)
            start_date_to: 开始时间的结束日期 (格式: YYYY-MM-DD)
            end_date_from: 结束时间的起始日期 (格式: YYYY-MM-DD)
            end_date_to: 结束时间的结束日期 (格式: YYYY-MM-DD)
            其他参数与 get_iterations 相同

        示例:
            # 查询2024年1月开始的迭代
            get_iterations_by_date_range(
                workspace_id="123",
                start_date_from="2024-01-01",
                start_date_to="2024-01-31"
            )

            # 查询2024年Q1结束的迭代
            get_iterations_by_date_range(
                workspace_id="123",
                end_date_from="2024-01-01",
                end_date_to="2024-03-31"
            )
        """
        from .time_utils import filter_items_by_time_field, compute_min_datetime_in_items, parse_datetime_bound
        logger = logging.getLogger("tapd_insight.iteration")
        query_params: Dict[str, Any] = {}
        query_params.update(params or {})

        # 构建时间范围查询参数
        if start_date_from:
            query_params["startdate"] = f">={start_date_from}"
        if start_date_to:
            # 如果已有 startdate 参数，需要组合查询
            if "startdate" in query_params:
                query_params["startdate"] = f"{query_params['startdate']}&startdate=<={start_date_to}"
            else:
                query_params["startdate"] = f"<={start_date_to}"

        if end_date_from:
            query_params["enddate"] = f">={end_date_from}"
        if end_date_to:
            # 如果已有 enddate 参数，需要组合查询
            if "enddate" in query_params:
                query_params["enddate"] = f"{query_params['enddate']}&enddate=<={end_date_to}"
            else:
                query_params["enddate"] = f"<={end_date_to}"

        # 翻页 + 端侧过滤（依据 startdate 与 enddate 组合）
        # 这里的业务语义：若设置 start_date_[from|to] 则基于 startdate 字段过滤；若设置 end_date_[from|to] 则基于 enddate 字段过滤。
        # 若两者都设置，则两重过滤（交集），并采用最严格的提前停止策略。
        page_no = page or 1
        page_size = limit or 200
        all_items: List[Dict[str, Any]] = []
        while True:
            page_items = self.get_iterations(
                workspace_id=workspace_id,
                fields=fields,
                limit=page_size,
                page=page_no,
                order=order,
                params=query_params,
            )
            if not page_items:
                break

            filtered = page_items
            violations_total = 0
            if start_date_from or start_date_to:
                filtered, v = filter_items_by_time_field(
                    filtered, field_name="startdate", start_text=start_date_from, end_text=start_date_to
                )
                violations_total += v
            if end_date_from or end_date_to:
                filtered, v = filter_items_by_time_field(
                    filtered, field_name="enddate", start_text=end_date_from, end_text=end_date_to
                )
                violations_total += v
            if violations_total:
                logger.warning(f"iterations_by_date_range 端侧越界过滤: {violations_total} 条 (page={page_no})")
            all_items.extend(filtered)

            # 提前停止：若当前页 startdate 的最小值早于 start_date_from，或 enddate 的最小值早于 end_date_from，则无需再翻
            stop = False
            if start_date_from:
                min_start = compute_min_datetime_in_items(page_items, field_name="startdate")
                sd = parse_datetime_bound(start_date_from, default_to_start=True)
                if min_start is not None and sd is not None and min_start < sd:
                    stop = True
            if end_date_from and not stop:
                min_end = compute_min_datetime_in_items(page_items, field_name="enddate")
                ed = parse_datetime_bound(end_date_from, default_to_start=True)
                if min_end is not None and ed is not None and min_end < ed:
                    stop = True
            if stop:
                break
            if len(page_items) < page_size:
                break
            page_no += 1
        return all_items

    def get_iteration_stories_overview(
        self,
        *,
        workspace_id: str | int,
        iteration_id: str | int,
        limit: int = 200,
        page: int = 1,
    ) -> List[IterationStory]:
        """以列表形式返回指定迭代内父级需求的概况。

        仅包含父需求（排除所有子需求），元素类型为 `IterationStory`。
        """
        # 延迟导入，避免循环依赖
        from .story import TapdStoryClient

        story_client = TapdStoryClient(self._auth, request_timeout_seconds=self._timeout)
        stories = story_client.get_stories_as_objects(
            workspace_id=workspace_id,
            fields=None,
            limit=limit,
            page=page,
            params={"iteration_id": str(iteration_id)},
            include_bug_stats=False,
            include_life_times=True,
        )

        # 仅保留父需求：优先使用对象方法 has_parent，如不存在则依据 parent_id 判断
        parent_stories: List[Story] = []
        for s in stories:
            has_parent_flag = False
            method = getattr(s, "has_parent", None)
            if callable(method):
                try:
                    has_parent_flag = bool(method())
                except Exception:
                    has_parent_flag = bool(getattr(s, "parent_id", None))
            else:
                has_parent_flag = bool(getattr(s, "parent_id", None))
            if not has_parent_flag:
                parent_stories.append(s)

        # 转换为 IterationStory
        result: List[IterationStory] = [IterationStory(**s.__dict__) for s in parent_stories]
        return result

    def get_iteration_stories_overview_markdown(
        self,
        *,
        workspace_id: str | int,
        iteration_id: str | int,
        limit: int = 200,
        page: int = 1,
    ) -> str:
        """以 Markdown 形式返回迭代内父级需求概况及统计信息。"""
        stories = self.get_iteration_stories_overview(
            workspace_id=workspace_id,
            iteration_id=iteration_id,
            limit=limit,
            page=page,
        )

        def esc(val: Optional[str]) -> str:
            text = val or ""
            text = str(text)
            text = text.replace("|", "\\|")
            text = text.replace("\n", " ")
            return text

        def to_float_size(val: Optional[str]) -> float:
            if val is None:
                return 0.0
            try:
                return float(str(val))
            except ValueError:
                return 0.0

        def is_completed_status(status_text: Optional[str]) -> bool:
            st = (status_text or "").strip()
            return st in {"已投产", "生产验收完成"}

        # 1) 迭代概览
        total_count = len(stories)
        total_size = sum(to_float_size(getattr(s, "size", None)) for s in stories)
        completed_count = sum(1 for s in stories if is_completed_status(getattr(s, "status", None)))
        incomplete_count = total_count - completed_count

        from datetime import datetime

        lines: List[str] = []
        lines.append("# 迭代概览")
        lines.append("")
        lines.append(f"- 迭代ID: {iteration_id}")
        lines.append(f"- 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("")
        lines.append("| 指标 | 数值 | 附加说明 |")
        lines.append("| --- | --- | --- |")
        lines.append(f"| 需求数量 | {total_count} | |")
        lines.append(f"| 需求总规模 | {total_size:.2f} | 所有父级需求规模求和 |")
        lines.append(f"| 完成需求数量 | {completed_count} | 状态为已投产或生产验收完成 |")
        lines.append(f"| 未完成需求数量 | {incomplete_count} | |")

        # 概览不再输出优先级分布子章节

        # 通用分组统计函数
        def sum_lifetime_hours(s: Story, status_names: List[str]) -> Optional[float]:
            """按给定状态集合汇总单个需求的时间成本（小时）。无数据返回 None。"""
            lts = getattr(s, "lifetimes", None)
            if not lts:
                return None
            status_set = set(status_names)
            total: float = 0.0
            matched = False
            for lt in lts:
                st = getattr(lt, "status", None)
                hours = getattr(lt, "time_cost_hours", None)
                if st in status_set and isinstance(hours, (int, float)):
                    total += float(hours)
                    matched = True
            return total if matched else None

        def avg(values: List[Optional[float]]) -> float:
            vals = [float(v) for v in values if isinstance(v, (int, float))]
            return sum(vals) / len(vals) if vals else 0.0

        def fmt(v: Optional[float]) -> str:
            return f"{float(v):.1f}" if isinstance(v, (int, float)) else ""

        # 各阶段对应的状态中文
        ST_REVIEW_PASSED = ["需求评审通过"]
        ST_TECH_DESIGN = ["技术方案评审"]
        ST_DEV = ["开发中", "代码评审"]
        ST_WAIT_TEST = ["已提测"]
        ST_TEST = ["测试中"]
        ST_UAT = ["待UAT验收"]
        ST_WAIT_RELEASE = ["UAT验收完成"]
        ST_PROD_ACCEPT = ["已投产"]

        # 计算各阶段平均值（仅对有该阶段记录的需求求平均）
        review_passed_hours = [sum_lifetime_hours(s, ST_REVIEW_PASSED) for s in stories]
        tech_design_hours = [sum_lifetime_hours(s, ST_TECH_DESIGN) for s in stories]
        dev_hours = [sum_lifetime_hours(s, ST_DEV) for s in stories]
        wait_test_hours = [sum_lifetime_hours(s, ST_WAIT_TEST) for s in stories]
        test_hours = [sum_lifetime_hours(s, ST_TEST) for s in stories]
        uat_hours = [sum_lifetime_hours(s, ST_UAT) for s in stories]
        wait_release_hours = [sum_lifetime_hours(s, ST_WAIT_RELEASE) for s in stories]
        prod_accept_hours = [sum_lifetime_hours(s, ST_PROD_ACCEPT) for s in stories]

        lines.append(f"| 需求评审通过平均滞留时间(小时) | {avg(review_passed_hours):.1f} | 仅统计有该阶段记录的需求 |")
        lines.append(f"| 平均技术方案设计时间(小时) | {avg(tech_design_hours):.1f} | 状态=技术方案评审 |")
        lines.append(f"| 平均开发时间(小时) | {avg(dev_hours):.1f} | 开发中+代码评审 |")
        lines.append(f"| 平均等待测试资源时间(小时) | {avg(wait_test_hours):.1f} | 状态=已提测 |")
        lines.append(f"| 平均测试时间(小时) | {avg(test_hours):.1f} | 状态=测试中 |")
        lines.append(f"| 平均UAT时间(小时) | {avg(uat_hours):.1f} | 状态=待UAT验收 |")
        lines.append(f"| 平均等待投产时长(小时) | {avg(wait_release_hours):.1f} | 状态=UAT验收完成 |")
        lines.append(f"| 平均生产验收时间(小时) | {avg(prod_accept_hours):.1f} | 状态=已投产 |")
        def add_group_section(title: str, group_values: List[str]) -> None:
            lines.append("")
            lines.append(f"## {title}")
            lines.append("")
            lines.append("| 分组 | 总数 | 规模总和 | 已完成 | 未完成 | 完成率 | 优先级分布 |")
            lines.append("| --- | ---: | ---: | ---: | ---: | ---: | --- |")
            # 分组统计
            stats: Dict[str, Dict[str, Any]] = {}
            for idx, key in enumerate(group_values):
                if not key:
                    key = "未设置"
                sref = stories[idx]
                if key not in stats:
                    stats[key] = {"total": 0, "done": 0, "size_sum": 0.0, "priorities": {}}
                stats[key]["total"] += 1
                stats[key]["size_sum"] += to_float_size(getattr(sref, "size", None))
                if is_completed_status(getattr(sref, "status", None)):
                    stats[key]["done"] += 1
                pr = getattr(sref, "priority_label", None) or "未设置"
                pr_map = stats[key]["priorities"]
                pr_map[pr] = pr_map.get(pr, 0) + 1

            # 按总数降序输出
            for k, v in sorted(stats.items(), key=lambda kv: (-kv[1]["total"], kv[0])):
                total = v["total"]
                done = v["done"]
                undone = total - done
                rate = (done / total * 100) if total > 0 else 0.0
                size_sum = v["size_sum"]
                pr_map = v["priorities"]
                pr_display = ", ".join([f"{esc(p)}:{c}" for p, c in sorted(pr_map.items(), key=lambda kv: (-kv[1], kv[0]))])
                lines.append(f"| {esc(k)} | {total} | {size_sum:.2f} | {done} | {undone} | {rate:.1f}% | {pr_display} |")

        # 工具函数：构建分组统计（含优先级与规模分布）
        def build_group_stats(group_keys: List[str], refs: List[Story]) -> Dict[str, Dict[str, Any]]:
            grouped: Dict[str, Dict[str, Any]] = {}
            for idx, key in enumerate(group_keys):
                key = key or "未设置"
                sref = refs[idx]
                entry = grouped.setdefault(key, {"total": 0, "done": 0, "size_sum": 0.0, "priorities": {}, "sizes": {}})
                entry["total"] += 1
                entry["size_sum"] += to_float_size(getattr(sref, "size", None))
                if is_completed_status(getattr(sref, "status", None)):
                    entry["done"] += 1
                pr = getattr(sref, "priority_label", None) or "未设置"
                entry["priorities"][pr] = entry["priorities"].get(pr, 0) + 1
                sz = getattr(sref, "size", None) or "未设置"
                entry["sizes"][sz] = entry["sizes"].get(sz, 0) + 1
            return grouped

        def format_distribution(dist: Dict[str, int], total: int) -> str:
            if total <= 0:
                return ""
            parts: List[str] = []
            for k, v in sorted(dist.items(), key=lambda kv: (-kv[1], kv[0])):
                pct = v / total * 100
                parts.append(f"{esc(k)}:{v}({pct:.1f}%)")
            return ", ".join(parts)

        # 2) 按优先级分组统计：新增占比列；去掉优先级分布列；新增规模分布列
        lines.append("")
        lines.append("# 迭代需求维度分析")
        lines.append("")
        lines.append("## 按优先级分组统计")
        lines.append("")
        lines.append("| 分组 | 总数 | 占比 | 规模总和 | 已完成 | 未完成 | 完成率 | 规模分布 |")
        lines.append("| --- | ---: | ---: | ---: | ---: | ---: | ---: | --- |")
        pr_keys = [getattr(s, "priority_label", None) or "未设置" for s in stories]
        pr_stats_full = build_group_stats(pr_keys, stories)
        for k, v in sorted(pr_stats_full.items(), key=lambda kv: (-kv[1]["total"], kv[0])):
            total = v["total"]
            share = (total / total_count * 100) if total_count > 0 else 0.0
            done = v["done"]
            undone = total - done
            rate = (done / total * 100) if total > 0 else 0.0
            size_sum = v["size_sum"]
            size_dist = format_distribution(v["sizes"], total)
            lines.append(f"| {esc(k)} | {total} | {share:.1f}% | {size_sum:.2f} | {done} | {undone} | {rate:.1f}% | {size_dist} |")

        # 3) 按规模分组统计：优先级分布加入占比
        lines.append("")
        lines.append("## 按规模分组统计")
        lines.append("")
        lines.append("| 分组 | 总数 | 规模总和 | 已完成 | 未完成 | 完成率 | 优先级分布 |")
        lines.append("| --- | ---: | ---: | ---: | ---: | ---: | --- |")
        size_keys = [getattr(s, "size", None) or "未设置" for s in stories]
        size_stats_full = build_group_stats(size_keys, stories)
        def _size_group_sort(item: tuple[str, Dict[str, Any]]):
            key = item[0]
            try:
                # 按数值从大到小排序
                return (0, -float(str(key)))
            except Exception:
                # 非数值（例如 未设置）排在数值分组之后，并按名称排序
                return (1, str(key))

        for k, v in sorted(size_stats_full.items(), key=_size_group_sort):
            total = v["total"]
            done = v["done"]
            undone = total - done
            rate = (done / total * 100) if total > 0 else 0.0
            size_sum = v["size_sum"]
            pr_dist = format_distribution(v["priorities"], total)
            lines.append(f"| {esc(k)} | {total} | {size_sum:.2f} | {done} | {undone} | {rate:.1f}% | {pr_dist} |")

        # 4) 按产品经理分组（对每位产品经理单独计数，若为空归为未设置）
        pm_flat: List[str] = []
        pm_story_ref: List[Story] = []
        for s in stories:
            pm = getattr(s, "product_manager", None)
            pm_name = (pm or "").strip() or "未设置"
            pm_flat.append(pm_name)
            pm_story_ref.append(s)

        # 将通用分组函数适配到产品经理扁平数据
        def add_pm_section() -> None:
            lines.append("")
            lines.append("## 按产品经理分组统计")
            lines.append("")
            lines.append("| 分组 | 总数 | 规模总和 | 已完成 | 未完成 | 完成率 | 规模分布 | 优先级分布 |")
            lines.append("| --- | ---: | ---: | ---: | ---: | ---: | --- | --- |")
            stats: Dict[str, Dict[str, Any]] = {}
            for idx, key in enumerate(pm_flat):
                if not key:
                    key = "未设置"
                sref = pm_story_ref[idx]
                if key not in stats:
                    stats[key] = {"total": 0, "done": 0, "size_sum": 0.0, "priorities": {}, "sizes": {}}
                stats[key]["total"] += 1
                stats[key]["size_sum"] += to_float_size(getattr(sref, "size", None))
                if is_completed_status(getattr(sref, "status", None)):
                    stats[key]["done"] += 1
                pr = getattr(sref, "priority_label", None) or "未设置"
                pr_map = stats[key]["priorities"]
                pr_map[pr] = pr_map.get(pr, 0) + 1
                sz = getattr(sref, "size", None) or "未设置"
                sz_map = stats[key]["sizes"]
                sz_map[sz] = sz_map.get(sz, 0) + 1
            for k, v in sorted(stats.items(), key=lambda kv: (-kv[1]["total"], kv[0])):
                total = v["total"]
                done = v["done"]
                undone = total - done
                rate = (done / total * 100) if total > 0 else 0.0
                size_sum = v["size_sum"]
                pr_display = format_distribution(v["priorities"], total)
                sz_display = format_distribution(v["sizes"], total)
                lines.append(f"| {esc(k)} | {total} | {size_sum:.2f} | {done} | {undone} | {rate:.1f}% | {sz_display} | {pr_display} |")

        add_pm_section()

        # 5) 按模块分组
        module_values = [getattr(s, "module", None) or "未设置" for s in stories]
        add_group_section("按模块分组统计", module_values)

        # 6) 未完成需求分状态输出
        lines.append("")
        lines.append("# 未完成需求分析")
        lines.append("")
        # 收集各未完成状态下的需求
        status_to_stories: Dict[str, List[Story]] = {}
        for s in stories:
            st = getattr(s, "status", None) or ""
            if not is_completed_status(st):
                status_to_stories.setdefault(st or "未设置", []).append(s)

        for st, lst in sorted(status_to_stories.items(), key=lambda kv: kv[0]):
            lines.append(f"## 状态：{esc(st or '未设置')}")
            lines.append("")
            lines.append("| 需求标题 | 规模 | 优先级 | 产品经理 | 研发负责人 | 开发者 | 测试负责人 | 测试人员 | 需求提出时间 | 需求评审通过时间 | 评审通过滞留(h) | 技术方案(h) | 开发(h) | 等待测试(h) | 测试(h) | UAT(h) | 等待投产(h) | 生产验收(h) | 未完成原因 |")
            lines.append("| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | ---: | ---: | ---: | ---: | ---: | ---: | ---: | ---: | --- |")
            for s in lst:
                name = esc(getattr(s, "name", None))
                size_val = esc(getattr(s, "size", None))
                pr_val = esc(getattr(s, "priority_label", None))
                pm_val = getattr(s, "product_manager", None)
                pms = esc(str(pm_val or "未设置"))
                tech_lead = esc(getattr(s, "tech_lead", None)) or ""
                test_lead = esc(getattr(s, "test_lead", None)) or ""
                developers = getattr(s, "developers", None)
                developers_str = esc(", ".join(developers)) if isinstance(developers, list) else esc(str(developers or ""))
                testers = getattr(s, "testers", None)
                testers_str = esc(", ".join(testers)) if isinstance(testers, list) else esc(str(testers or ""))
                created = esc(getattr(s, "created", None))
                review_passed = esc(getattr(s, "review_passed", None))
                _r = sum_lifetime_hours(s, ST_REVIEW_PASSED)
                _t = sum_lifetime_hours(s, ST_TECH_DESIGN)
                _d = sum_lifetime_hours(s, ST_DEV)
                _wt = sum_lifetime_hours(s, ST_WAIT_TEST)
                _ts = sum_lifetime_hours(s, ST_TEST)
                _uat = sum_lifetime_hours(s, ST_UAT)
                _wr = sum_lifetime_hours(s, ST_WAIT_RELEASE)
                _pa = sum_lifetime_hours(s, ST_PROD_ACCEPT)
                lines.append(f"| {name} | {size_val} | {pr_val} | {pms} | {tech_lead} | {developers_str} | {test_lead} | {testers_str} | {created} | {review_passed} | {fmt(_r)} | {fmt(_t)} | {fmt(_d)} | {fmt(_wt)} | {fmt(_ts)} | {fmt(_uat)} | {fmt(_wr)} | {fmt(_pa)} |  |")

        # 附录：明细表
        lines.append("")
        lines.append("# 附录：需求明细")
        lines.append("")
        lines.append("| 需求名称 | 需求id | 规模 | 优先级 | 产品经理 | 需求创建时间 | 需求评审通过时间 | 需求状态 | 需求研发负责人 | 需求测试负责人 | 评审通过滞留(h) | 技术方案(h) | 开发(h) | 等待测试(h) | 测试(h) | UAT(h) | 等待投产(h) | 生产验收(h) |")
        lines.append("| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | ---: | ---: | ---: | ---: | ---: | ---: | ---: | ---: |")
        # 按规模从大到小排序，无法解析为数值的规模按 0 处理，排在后面
        stories_sorted = sorted(
            stories,
            key=lambda s: (-to_float_size(getattr(s, "size", None)), str(getattr(s, "name", "")))
        )
        for s in stories_sorted:
            name = esc(getattr(s, "name", None))
            sid = esc(getattr(s, "id", None))
            size_val = esc(getattr(s, "size", None))
            pr_val = esc(getattr(s, "priority_label", None))
            pm_val = getattr(s, "product_manager", None)
            pms = esc(str(pm_val or ""))
            created = esc(getattr(s, "created", None))
            review_passed = esc(getattr(s, "review_passed", None))
            status_text = esc(getattr(s, "status", None))
            tech_lead = esc(getattr(s, "tech_lead", None))
            test_lead = esc(getattr(s, "test_lead", None))
            _r = sum_lifetime_hours(s, ST_REVIEW_PASSED)
            _t = sum_lifetime_hours(s, ST_TECH_DESIGN)
            _d = sum_lifetime_hours(s, ST_DEV)
            _wt = sum_lifetime_hours(s, ST_WAIT_TEST)
            _ts = sum_lifetime_hours(s, ST_TEST)
            _uat = sum_lifetime_hours(s, ST_UAT)
            _wr = sum_lifetime_hours(s, ST_WAIT_RELEASE)
            _pa = sum_lifetime_hours(s, ST_PROD_ACCEPT)
            lines.append(f"| {name} | {sid} | {size_val} | {pr_val} | {pms} | {created} | {review_passed} | {status_text} | {tech_lead} | {test_lead} | {fmt(_r)} | {fmt(_t)} | {fmt(_d)} | {fmt(_wt)} | {fmt(_ts)} | {fmt(_uat)} | {fmt(_wr)} | {fmt(_pa)} |")

        return "\n".join(lines)

def demo_iteration_stories_overview() -> None:
    """演示：输出迭代 1148986738001001948 的父级需求概况列表。"""
    import os
    from datetime import datetime
    from .config import load_tapd_config

    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(
        client_id=cfg.client_id,
        client_secret=cfg.client_secret,
    )

    client = TapdIterationClient(auth)
    markdown_table = client.get_iteration_stories_overview_markdown(
        workspace_id=cfg.workspace_id,
        iteration_id="1148986738001001948",
        limit=200,
        page=1,
    )

    # 生成文件名，包含时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"iteration_1148986738001001948_stories_{timestamp}.md"

    # 获取当前工作目录并构建完整路径
    current_dir = os.getcwd()
    file_path = os.path.join(current_dir, filename)

    # 写入文件
    try:
        with open(file_path, 'w', encoding='utf-8') as f:

            f.write(markdown_table)

        logger.info("\n迭代父级需求概况已输出到文件:")
        logger.info(f"文件路径: {file_path}")
        logger.info("=" * 60)
    except Exception as e:
        logger.error(f"写入文件失败: {e}")
        logger.info("\n迭代父级需求概况 (控制台输出):")
        logger.info("=" * 60)
        logger.info(markdown_table)
        logger.info("=" * 60)


if __name__ == "__main__":
    # 直接从同包内导入，避免再次触发包级懒加载逻辑
    # from .auth import TapdClientCredentialsAuth
    # from .config import load_tapd_config

    # cfg = load_tapd_config()
    # auth = TapdClientCredentialsAuth(
    #     client_id=cfg.client_id,
    #     client_secret=cfg.client_secret,
    # )

    # client = TapdIterationClient(auth)

    # # 查询迭代列表
    # iterations = client.get_iterations(
    #     workspace_id=cfg.workspace_id,
    #     fields="id,name,status,startdate,enddate",
    #     order="created desc",
    #     limit=10,
    #     page=1,
    # )
    # print("iterations:", iterations)

    # # 按 ID 查询迭代
    # if iterations:
    #     iteration_id = iterations[0]["id"]
    #     iteration = client.get_iteration_by_id(
    #         workspace_id=cfg.workspace_id,
    #         iteration_id=iteration_id,
    #         fields="id,name,status,startdate,enddate",
    #     )
    #     print("iteration:", iteration)

    # # 按时间范围查询迭代
    # date_range_iterations = client.get_iterations_by_date_range(
    #     workspace_id=cfg.workspace_id,
    #     start_date_from="2025-08-01",
    #     start_date_to="2025-08-31",
    #     fields="id,name,status,startdate,enddate",
    #     order="startdate desc",
    #     limit=5,
    # )
    # print("2025年8月份的迭代:", date_range_iterations)

    # # 查询Q1结束的迭代
    # q1_end_iterations = client.get_iterations_by_date_range(
    #     workspace_id=cfg.workspace_id,
    #     end_date_from="2025-01-01",
    #     end_date_to="2025-03-31",
    #     fields="id,name,status,startdate,enddate",
    #     order="enddate desc",
    #     limit=3,
    # )
    # print("2025年Q1结束的迭代:", q1_end_iterations)

    # 新增演示：输出指定迭代的父级需求概况
    demo_iteration_stories_overview()


