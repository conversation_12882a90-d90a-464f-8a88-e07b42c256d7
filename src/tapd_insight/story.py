"""
TAPD 需求管理：查询需求列表与按 ID 查询需求。

参考文档：
https://open.tapd.cn/document/api-doc/API%E6%96%87%E6%A1%A3/api_reference/story/get_stories.html

使用标准库实现 HTTP 请求，依赖 `TapdClientCredentialsAuth` 提供的 Bearer Token。
"""

from __future__ import annotations

import json
import logging
from typing import Any, Dict, List, Optional, TYPE_CHECKING
from dataclasses import dataclass, field
from urllib import parse
from datetime import datetime

import requests

from .auth import TapdClientCredentialsAuth
from .transport import TapdApiTransport
from .measure import TapdMeasureClient, LifeTime

# 仅用于类型检查，避免运行时循环导入
if TYPE_CHECKING:
    from .bug import Bug

# https://open.tapd.cn/document/api-doc/API%E6%96%87%E6%A1%A3/api_reference/story/get_stories.html
DEFAULT_STORIES_URL = "https://api.tapd.cn/stories"
# https://open.tapd.cn/document/api-doc/API%E6%96%87%E6%A1%A3/api_reference/story/get_story_related_bugs.html
DEFAULT_STORY_RELATED_BUGS_URL = "https://api.tapd.cn/stories/get_related_bugs"


class TapdApiError(Exception):
    """TAPD API 调用错误。"""

    def __init__(self, message: str, *, status_code: Optional[int] = None, payload: Optional[Dict[str, Any]] = None) -> None:
        super().__init__(message)
        self.status_code = status_code
        self.payload = payload


def get_status_display(status_key: str) -> str:
    """将状态枚举值转换为中文描述。"""
    status_mapping = {
        "planning": "规划中",
        "developing": "开发中",
        "resolved": "已实现",
        "status_2": "已提测",
        "status_3": "已投产",
        "status_4": "生产验收完成",
        "status_6": "新需求",
        "status_7": "需求评审通过",
        "status_8": "技术方案评审",
        "status_9": "挂起",
        "status_10": "测试中",
        "status_11": "待UAT验收",
        "status_12": "UAT验收完成",
        "status_13": "已取消",
        "status_14": "代码评审",
        "status_16": "需求方案确认",
        "status_17": "冒烟未通过",
        "status_18": "UAT未通过",
        "status_19": "待评审",
        "status_20": "已规划",
        "status_21": "实现中",
        "status_28": "技术方案设计",
    }
    return status_mapping.get(status_key, status_key)


@dataclass
class FixerBugAggregate:
    """修复人维度的缺陷聚合信息。

    仅统计有效缺陷。
    """

    total: int = 0
    reopen_once: int = 0
    reopen_multi: int = 0
    fat_bugs: int = 0
    uat_bugs: int = 0
    prod_bugs: int = 0
    prod_verify_bugs: int = 0

@dataclass
class ReporterBugAggregate:
    """报告人维度的缺陷聚合信息。

    仅统计有效缺陷。
    """

    total: int = 0
    valid_bugs: int = 0

@dataclass
class StoryRelatedBug:
    """需求关联缺陷关系实体。"""

    workspace_id: str
    story_id: str
    bug_id: str


@dataclass
class StoryBugStats:
    """需求缺陷统计信息。"""

    fat_bugs: int = 0 # fat 环境有效缺陷数
    uat_bugs: int = 0 # uat 环境有效缺陷数
    prod_bugs: int = 0 # 生产环境有效缺陷数
    prod_verify_bugs: int = 0 # 生产验证环境有效缺陷数
    valid_bugs: int = 0  # 有效缺陷数（非rejected状态的缺陷）
    invalid_bugs: int = 0  # 无效缺陷数

    # 新增：持有构成统计的全部缺陷对象
    bugs: List["Bug"] = field(default_factory=list)
    # 修复人分组统计（聚合后的结构）
    fixer_aggregates: Dict[str, FixerBugAggregate] = field(default_factory=dict)
    # 新增：报告人分组统计（聚合：总缺陷与有效缺陷数）
    reporter_counts: Dict[str, ReporterBugAggregate] = field(default_factory=dict)


@dataclass
class Story:
    """结构化的 TAPD 需求对象。"""

    id: str
    name: Optional[str]
    priority_label: Optional[str]
    status: Optional[str]
    v_status: Optional[str]
    module: Optional[str]
    size: Optional[str]  # 规模
    owner: Optional[str]
    iteration_id: Optional[str]
    category_id: Optional[str]
    ancestor_id: Optional[str]  # 祖先需求
    parent_id: Optional[str]  # 父需求id
    children_id: Optional[List[str]]  # 子需求id列表
    workspace_id: Optional[str]
    testers: List[str] # 参与的测试人员
    test_lead: Optional[str] # 开发负责人
    developers: List[str] # 开发者列表
    tech_lead: Optional[str] # 研发负责人
    product_manager: Optional[str] # 产品经理（仅一个）
    created: Optional[str]  # 创建时间
    review_passed: Optional[str]  # 需求评审通过 2025-08-19 14:05
    begin: Optional[str]  # 预计开始
    due: Optional[str]  # 预计结束
    tech_design_start: Optional[str]  # 技术方案设计开始,对应custom_field_26字段
    tech_design_end: Optional[str]  # 技术方案设计结束,对应custom_field_27字段
    dev_start: Optional[str]  # 开发开始,对应custom_field_28字段
    test_start: Optional[str]  # 测试开始,对应custom_field_29字段
    planned_test_date: Optional[str]  # 预计提测
    actual_test_date: Optional[str]  # 实际提测
    planned_test_complete_date: Optional[str]  # 预计测试完成
    actual_test_complete_date: Optional[str]  # 实际测试完成
    planned_uat_complete_date: Optional[str]  # 预计UAT完成
    actual_uat_complete_date: Optional[str]  # 实际UAT完成
    planned_release_date: Optional[str]  # 预计上线
    effort: Optional[float]  # 工作量估算
    effort_completed: Optional[float]  # 已完成工作量
    remain: Optional[float]  # 剩余工作量
    exceed: Optional[float]  # 超出工作量

    # 导出属性
    has_children: Optional[bool] # 是否有子需求
    bug_stats: Optional[StoryBugStats] # 缺陷统计整体信息

    # 延期相关属性
    dev_is_delayed: Optional[bool] # 开发是否延期
    dev_delay_days: Optional[int] # 开发延期天数
    test_is_delayed: Optional[bool] # 测试是否延期
    test_delay_days: Optional[int] # 测试延期天数
    uat_is_delayed: Optional[bool] # UAT是否延期
    uat_delay_days: Optional[int] # UAT延期天数

    # 质量指标
    escape_rate_percent: Optional[float] # 缺陷逃逸率，(prod_bugs+prod_verify_bugs)/有效缺陷*100%
    prod_verify_escape_rate_percent: Optional[float] # 生产验证环境缺陷逃逸率，prod_verify_bugs/有效缺陷*100%
    defect_density: Optional[float] # 缺陷密度 = 有效缺陷数 / 故事点

    # 测试用时（天）= 实际测试完成时间 - 实际提测时间
    test_duration_days: Optional[int]
    # UAT用时（天）= 实际UAT完成时间 - 实际提测时间
    uat_duration_days: Optional[int]
    # 状态流转时间线
    lifetimes: List[LifeTime] = field(default_factory=list)

    def has_parent(self) -> bool:
        """判断是否存在父需求。"""
        return self.parent_id is not None and self.parent_id != '0'

    @staticmethod
    def from_api(
        story_dict: Dict[str, Any],
        bug_stats: Optional[StoryBugStats] = None,
        lifetimes: Optional[List[LifeTime]] = None,
    ) -> "Story":
        def to_str(val: Any) -> Optional[str]:
            if val is None:
                return None
            return str(val)

        def to_float(val: Any) -> Optional[float]:
            if val is None or val == "":
                return None
            try:
                return float(val)
            except (TypeError, ValueError):
                return None

        def split_multi(val: Any) -> List[str]:
            raw = to_str(val) or ""
            if not raw:
                return []
            tokens: List[str] = []
            for sep in [",", "，", "|", ";", "；", "、"]:
                raw = raw.replace(sep, ",")
            for token in raw.split(","):
                token = token.strip()
                if token:
                    tokens.append(token)
            return tokens

        def pick_float(*keys: str) -> Optional[float]:
            for k in keys:
                v = story_dict.get(k)
                f = to_float(v)
                if f is not None:
                    return f
            return None

        def pick_bool_default_false(*keys: str) -> Optional[bool]:
            for k in keys:
                v = story_dict.get(k)
                if isinstance(v, bool):
                    return v
                s = to_str(v)
                if s is None:
                    continue
                if s.lower() in {"true", "1", "yes", "y"}:
                    return True
                if s.lower() in {"false", "0", "no", "n"}:
                    return False
                try:
                    if int(s) > 0:
                        return True
                except ValueError:
                    pass
            return None

        def parse_children_id(val: Any) -> Optional[List[str]]:
            """解析子需求ID列表，格式可能是 ||1148986738001066096|1148986738001066097|1148986738001066098"""
            raw = to_str(val)
            if not raw:
                return None
            # 移除开头的 ||，然后按 | 分割
            if raw.startswith("||"):
                raw = raw[2:]
            elif raw.startswith("|"):
                raw = raw[1:]

            if not raw:
                return None

            children = []
            for child_id in raw.split("|"):
                child_id = child_id.strip()
                if child_id:
                    children.append(child_id)

            return children if children else None

        def parse_date_to_datetime(date_str: Optional[str]) -> Optional[datetime]:
            """解析日期字符串为datetime对象。

            支持的格式：
            - "2025-08-08"
            - "2025-08-14 09:57"
            """
            if not date_str:
                return None

            date_str = date_str.strip()
            if not date_str:
                return None

            try:
                # 尝试解析 "2025-08-08" 格式
                if len(date_str) == 10 and date_str.count('-') == 2:
                    return datetime.strptime(date_str, "%Y-%m-%d")
                # 尝试解析 "2025-08-14 09:57" 格式
                elif len(date_str) == 16 and date_str.count('-') == 2 and ' ' in date_str:
                    return datetime.strptime(date_str, "%Y-%m-%d %H:%M")
                # 尝试解析其他可能的格式
                else:
                    # 如果包含时间，尝试更通用的格式
                    if ' ' in date_str:
                        # 可能的格式: "2025-08-14 09:57:30"
                        return datetime.strptime(date_str[:16], "%Y-%m-%d %H:%M")
                    else:
                        # 纯日期格式
                        return datetime.strptime(date_str, "%Y-%m-%d")
            except ValueError:
                # 解析失败，返回None
                return None

        def calculate_delay(actual_date_str: Optional[str], planned_date_str: Optional[str]) -> tuple[Optional[bool], Optional[int]]:
            """计算延期情况。

            返回：(是否延期, 延期天数)
            - 如果任一日期为空，返回 (None, None)
            - 如果实际日期晚于预计日期，返回 (True, 延期天数)
            - 如果实际日期早于或等于预计日期，返回 (False, 0或负数)
            """
            actual_date = parse_date_to_datetime(actual_date_str)
            planned_date = parse_date_to_datetime(planned_date_str)

            if actual_date is None or planned_date is None:
                return None, None

            # 计算延期天数
            delay_delta = actual_date - planned_date
            delay_days = delay_delta.days

            # 判断是否延期
            is_delayed = delay_days > 0

            return is_delayed, delay_days


        # 集中读取多次使用的字段
        sid = to_str(story_dict.get("id")) or ""
        cf_one_testers = story_dict.get("custom_field_one")
        cf_six_tech_leads = story_dict.get("custom_field_six")
        developer_raw = story_dict.get("developer")
        children_id_raw = story_dict.get("children_id")
        cf10_planned_test_date = story_dict.get("custom_field_10")
        cf11_planned_release_date = story_dict.get("custom_field_11")
        cf13_planned_test_complete = story_dict.get("custom_field_13")
        cf14_planned_uat_complete = story_dict.get("custom_field_14")
        cf15_actual_test_date = story_dict.get("custom_field_15")
        cf16_actual_uat_complete = story_dict.get("custom_field_16")
        cf17_actual_test_complete = story_dict.get("custom_field_17")
        cf18_review_passed = story_dict.get("custom_field_18")
        cf19_product_managers = story_dict.get("custom_field_19")
        cf26_tech_design_start = story_dict.get("custom_field_26")
        cf27_tech_design_end = story_dict.get("custom_field_27")
        cf28_dev_start = story_dict.get("custom_field_28")
        cf29_test_start = story_dict.get("custom_field_29")

        # 衍生信息
        tech_leads = split_multi(cf_six_tech_leads)
        if not tech_leads:
            tech_leads = split_multi(developer_raw)
            if tech_leads:
                tech_leads = [tech_leads[0]]

        testers = split_multi(cf_one_testers)
        # 产品经理字段可能是多值，仅取第一个
        pm_list = split_multi(cf19_product_managers)
        product_manager_value = str(pm_list[0]) if pm_list else None

        children_id_value = parse_children_id(children_id_raw)

        # 计算延期信息
        dev_is_delayed, dev_delay_days = calculate_delay(
            cf15_actual_test_date,
            cf10_planned_test_date
        )

        test_is_delayed, test_delay_days = calculate_delay(
            cf17_actual_test_complete,
            cf13_planned_test_complete
        )

        uat_is_delayed, uat_delay_days = calculate_delay(
            cf16_actual_uat_complete,
            cf14_planned_uat_complete
        )

        # 质量指标计算
        # 总有效缺陷数
        total_valid_bugs = bug_stats.valid_bugs if bug_stats else None
        # 逃逸率相关
        escape_rate_percent = None
        prod_verify_escape_rate_percent = None
        if bug_stats and total_valid_bugs and total_valid_bugs > 0:
            prod_cnt = bug_stats.prod_bugs or 0
            prod_verify_cnt = bug_stats.prod_verify_bugs or 0
            escape_rate_percent = (prod_cnt + prod_verify_cnt) / float(total_valid_bugs) * 100.0
            prod_verify_escape_rate_percent = prod_verify_cnt / float(total_valid_bugs) * 100.0

        # 缺陷密度：有效缺陷数 / 故事点（规模）
        defect_density = None
        try:
            size_points = None
            raw_size = story_dict.get("size")
            if raw_size is not None and str(raw_size).strip() != "":
                size_points = float(str(raw_size))
            if total_valid_bugs is not None and size_points and size_points > 0:
                defect_density = float(total_valid_bugs) / size_points
        except (TypeError, ValueError):
            defect_density = None

        # 测试用时（天）：actual_test_complete_date - actual_test_date
        test_duration_days: Optional[int] = None
        try:
            dt_start = parse_date_to_datetime(to_str(cf15_actual_test_date))
            dt_end = parse_date_to_datetime(to_str(cf17_actual_test_complete))
            if dt_start is not None and dt_end is not None:
                test_duration_days = (dt_end - dt_start).days
        except Exception:
            test_duration_days = None

        # UAT用时（天）：actual_uat_complete_date - planned_uat_complete_date
        uat_duration_days: Optional[int] = None
        try:
            dt_start = parse_date_to_datetime(to_str(cf17_actual_test_complete))
            dt_end = parse_date_to_datetime(to_str(cf16_actual_uat_complete))
            if dt_start is not None and dt_end is not None:
                uat_duration_days = (dt_end - dt_start).days
        except Exception:
            uat_duration_days = None

        return Story(
            id=sid,
            name=to_str(story_dict.get("name")),
            priority_label=to_str(story_dict.get("priority_label")),
            status=get_status_display(to_str(story_dict.get("status")) or ""),
            v_status=to_str(story_dict.get("v_status")),
            module=to_str(story_dict.get("module")),
            size=to_str(story_dict.get("size")),
            owner=to_str(story_dict.get("owner")),
            developers=split_multi(story_dict.get("developer")),
            iteration_id=to_str(story_dict.get("iteration_id")),
            category_id=to_str(story_dict.get("workitem_type_id")) or to_str(story_dict.get("category_id")),
            ancestor_id=to_str(story_dict.get("ancestor_id")),
            parent_id=to_str(story_dict.get("parent_id")),
            children_id=children_id_value,
            has_children=len(children_id_value) > 0 if children_id_value else False,
            workspace_id=to_str(story_dict.get("workspace_id")),
            testers=testers,
            test_lead=str(testers[0]) if testers else None,
            tech_lead=str(tech_leads[0]) if tech_leads else None,
            product_manager=product_manager_value,
            review_passed=to_str(cf18_review_passed),
            created=to_str(story_dict.get("created")),
            begin=to_str(story_dict.get("begin")),
            due=to_str(story_dict.get("due")),
            planned_test_date=to_str(cf10_planned_test_date),
            actual_test_date=to_str(cf15_actual_test_date),
            planned_test_complete_date=to_str(cf13_planned_test_complete),
            actual_test_complete_date=to_str(cf17_actual_test_complete),
            planned_uat_complete_date=to_str(cf14_planned_uat_complete),
            actual_uat_complete_date=to_str(cf16_actual_uat_complete),
            planned_release_date=to_str(cf11_planned_release_date),
            effort=to_float(story_dict.get("effort")),
            effort_completed=to_float(story_dict.get("effort_completed")),
            remain=to_float(story_dict.get("remain")),
            exceed=to_float(story_dict.get("exceed")),
            bug_stats=bug_stats,
            dev_is_delayed=dev_is_delayed,
            dev_delay_days=dev_delay_days,
            test_is_delayed=test_is_delayed,
            test_delay_days=test_delay_days,
            uat_is_delayed=uat_is_delayed,
            uat_delay_days=uat_delay_days,
            escape_rate_percent=escape_rate_percent,
            prod_verify_escape_rate_percent=prod_verify_escape_rate_percent,
            defect_density=defect_density,
            test_duration_days=test_duration_days,
            uat_duration_days=uat_duration_days,
            tech_design_start=to_str(cf26_tech_design_start),
            tech_design_end=to_str(cf27_tech_design_end),
            dev_start=to_str(cf28_dev_start),
            test_start=to_str(cf29_test_start),
            lifetimes=lifetimes or [],
        )


class TapdStoryClient:
    """TAPD 需求客户端，仅提供查询相关能力。"""

    def __init__(
        self,
        auth: TapdClientCredentialsAuth,
        *,
        stories_url: str = DEFAULT_STORIES_URL,
        related_bugs_url: str = DEFAULT_STORY_RELATED_BUGS_URL,
        request_timeout_seconds: float = 10.0,
    ) -> None:
        """初始化需求客户端。

        参数说明：
        auth 为已配置好的授权客户端，用于提供 Authorization 头。
        stories_url 为需求查询接口的基础 URL。
        related_bugs_url 为查询需求关联缺陷接口 URL。
        request_timeout_seconds 为请求超时秒数。
        """
        self._auth = auth
        self._stories_url = stories_url
        self._related_bugs_url = related_bugs_url
        self._timeout = request_timeout_seconds
        self._transport = TapdApiTransport(request_timeout_seconds=request_timeout_seconds)

    def get_story_life_times(self, *, workspace_id: str | int, story_id: str | int) -> List[LifeTime]:
        """封装 measure 接口，查询单个需求的状态流转时间线，按创建时间降序。"""
        measure_client = TapdMeasureClient(self._auth)
        lifetimes = measure_client.get_life_times(
            workspace_id=workspace_id,
            entity_type="story",
            entity_id=str(story_id),
            order="created desc",
            limit=200,
            page=1,
        )
        lifetimes = [lt for lt in lifetimes if (lt.change_from or "").lower() == "status"]
        for lt in lifetimes:
            if lt.status:
                lt.status = get_status_display(lt.status)
        # 对于同一次状态的变更事件（可能会返回多条，仅 owner 不同），按 (status, begin_date, end_date, created) 去重
        unique: List[LifeTime] = []
        seen: set[tuple[str, str, str, str]] = set()
        for lt in lifetimes:
            key = (
                lt.status or "",
                lt.begin_date or "",
                lt.end_date or "",
                lt.created or "",
            )
            if key in seen:
                continue
            seen.add(key)
            unique.append(lt)
        return unique

    def _get_stories(
        self,
        *,
        workspace_id: str | int,
        fields: Optional[str] = None,
        limit: Optional[int] = None,
        page: Optional[int] = None,
        order: Optional[str] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """查询需求列表。

        仅做一次请求，不做自动翻页。若需更多记录，可调整 limit 与 page。
        对返回结构进行轻度扁平化，提取每项中的 `Story` 字段。
        """
        query: Dict[str, Any] = {}
        query.update(params or {})
        query["workspace_id"] = str(workspace_id)

        if fields:
            query["fields"] = fields
        if limit is not None:
            query["limit"] = int(limit)
        if page is not None:
            query["page"] = int(page)
        if order:
            query["order"] = order

        # 日志：请求 info，头部 debug
        logger = logging.getLogger("tapd_insight.story")
        logger.info(f"GET {self._stories_url} {query}")
        qs = parse.urlencode(query, doseq=True)
        url = f"{self._stories_url}?{qs}" if qs else self._stories_url

        headers = {
            "Authorization": self._auth.get_authorization_header(),
            "Accept": "application/json",
        }

        logger.info(f"GET {url}")
        logger.debug(f"Headers: {self._mask_sensitive_headers(headers)}")

        try:
            resp = self._transport.request("GET", url, headers=headers)
            status_code = resp.status_code
            payload = resp.json()

            # 响应：状态 info，payload debug
            logger.info(f"响应 {status_code} GET {url}")
            try:
                logger.debug(json.dumps(payload, ensure_ascii=False, indent=2))
            except Exception:
                logger.debug("Response JSON: <unavailable>")
        except requests.exceptions.HTTPError as e:
            details = None
            try:
                details = resp.text[:500]
            except Exception:
                details = None
            logger.error(
                f"HTTP {status_code} 调用 TAPD stories 接口失败"
            )
            raise TapdApiError(
                f"HTTP {status_code} 调用 TAPD stories 接口失败",
                status_code=status_code,
                payload={"details": details},
            ) from e
        except requests.exceptions.RequestException as e:
            logging.getLogger("tapd_insight.story").error("无法连接 TAPD stories 接口")
            raise TapdApiError("无法连接 TAPD stories 接口", payload={"reason": str(e)}) from e

        if not isinstance(payload, dict) or payload.get("status") != 1:
            raise TapdApiError("TAPD stories 接口返回业务状态异常", status_code=status_code, payload=payload)

        data = payload.get("data")
        if not isinstance(data, list):
            return []

        stories: List[Dict[str, Any]] = []
        for item in data:
            if isinstance(item, dict) and "Story" in item and isinstance(item["Story"], dict):
                stories.append(item["Story"])
        return stories

    def _get_story_by_id(self, *, workspace_id: str | int, story_id: str | int, fields: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """按 ID 查询单个需求。

        若未查询到，返回 None。
        """
        result = self._get_stories(
            workspace_id=workspace_id,
            fields=fields,
            limit=1,
            params={"id": str(story_id)},
        )
        return result[0] if result else None

    def _get_stories_by_ids(
        self,
        *,
        workspace_id: str | int,
        story_ids: List[str | int],
        fields: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        按多个需求 ID 批量查询需求原始字典列表。

        依据官方文档，`id` 支持多ID查询，单次最大 200 条，超过将按 200 拆分多次请求并聚合结果。
        文档参考：`https://open.tapd.cn/document/api-doc/API文档/api_reference/story/get_stories.html`。
        """
        if not story_ids:
            return []
        seen: set[str] = set()
        cleaned: List[str] = []
        for sid in story_ids:
            s = str(sid).strip()
            if not s or s in seen:
                continue
            seen.add(s)
            cleaned.append(s)
        if not cleaned:
            return []
        all_items: List[Dict[str, Any]] = []
        batch_size = 200
        for i in range(0, len(cleaned), batch_size):
            chunk = cleaned[i:i + batch_size]
            try:
                page_items = self._get_stories(
                    workspace_id=workspace_id,
                    fields=fields,
                    limit=min(len(chunk), batch_size),
                    page=1,
                    params={"id": ",".join(chunk)},
                )
            except Exception:
                logging.getLogger("tapd_insight.story").exception(
                    f"get_stories_by_ids failed on chunk {i//batch_size+1} size={len(chunk)}"
                )
                page_items = []
            if page_items:
                all_items.extend(page_items)
        return all_items

    # 对象化返回版本
    def get_stories_as_objects(
        self,
        *,
        workspace_id: str | int,
        fields: Optional[str] = None,
        limit: Optional[int] = None,
        page: Optional[int] = None,
        order: Optional[str] = None,
        params: Optional[Dict[str, Any]] = None,
        include_bug_stats: bool = False,
        include_life_times: bool = False,
    ) -> List[Story]:
        raw = self._get_stories(
            workspace_id=workspace_id,
            fields=fields,
            limit=limit,
            page=page,
            order=order,
            params=params,
        )

        stories = []
        for item in raw:
            story_id = item.get("id")
            bug_stats_obj = None
            lifetimes_obj: Optional[List[LifeTime]] = None
            if include_bug_stats and story_id:
                bug_stats_obj = self.get_story_bug_stats(workspace_id=workspace_id, story_id=story_id)
            if include_life_times and story_id:
                lifetimes_obj = self.get_story_life_times(workspace_id=workspace_id, story_id=story_id)
            stories.append(Story.from_api(item, bug_stats_obj, lifetimes_obj))

        return stories

    def get_stories_by_ids_as_objects(
        self,
        *,
        workspace_id: str | int,
        story_ids: List[str | int],
        fields: Optional[str] = None,
        include_bug_stats: bool = False,
        include_life_times: bool = False,
    ) -> List[Story]:
        """
        按多个需求 ID 批量查询并返回 Story 对象列表。
        其他参数口径与 get_story_by_id_as_object 保持一致。
        """
        raws = self._get_stories_by_ids(workspace_id=workspace_id, story_ids=story_ids, fields=fields)
        stories: List[Story] = []
        for item in raws:
            sid = item.get("id")
            bug_stats_obj = None
            lifetimes_obj: Optional[List[LifeTime]] = None
            if include_bug_stats and sid:
                bug_stats_obj = self.get_story_bug_stats(workspace_id=workspace_id, story_id=sid)
            if include_life_times and sid:
                lifetimes_obj = self.get_story_life_times(workspace_id=workspace_id, story_id=sid)
            stories.append(Story.from_api(item, bug_stats_obj, lifetimes_obj))
        return stories

    def get_story_by_id_as_object(self, *, workspace_id: str | int, story_id: str | int, fields: Optional[str] = None, include_bug_stats: bool = False, include_life_times: bool = False) -> Optional[Story]:
        raw = self._get_story_by_id(workspace_id=workspace_id, story_id=story_id, fields=fields)
        if not raw:
            return None

        bug_stats = self.get_story_bug_stats(workspace_id=workspace_id, story_id=story_id) if include_bug_stats else None
        lifetimes = self.get_story_life_times(workspace_id=workspace_id, story_id=story_id) if include_life_times else None
        return Story.from_api(raw, bug_stats, lifetimes)

    def _get_story_by_tester(
        self,
        *,
        workspace_id: str | int,
        tester: str,
        fields: Optional[str] = None,
        limit: Optional[int] = None,
        page: Optional[int] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """根据测试人员查询需求列表。

        参数说明：
        workspace_id: 工作空间ID
        tester: 测试人员姓名
        fields: 返回字段列表
        limit: 返回记录数限制
        page: 页码
        params: 其他查询参数

        返回：按创建时间降序排序的需求列表
        """
        query_params = params.copy() if params else {}
        query_params["custom_field_one"] = tester

        return self._get_stories(
            workspace_id=workspace_id,
            fields=fields,
            limit=limit,
            page=page,
            order="created desc",
            params=query_params,
        )

    def get_story_by_tester_as_object(
        self,
        *,
        workspace_id: str | int,
        tester: str,
        fields: Optional[str] = None,
        limit: Optional[int] = None,
        page: Optional[int] = None,
        params: Optional[Dict[str, Any]] = None,
        include_bug_stats: bool = False,
        include_life_times: bool = False,
    ) -> List[Story]:
        """根据测试人员查询需求列表，返回 Story 对象列表。

        参数说明：
        workspace_id: 工作空间ID
        tester: 测试人员姓名
        fields: 返回字段列表
        limit: 返回记录数限制
        page: 页码
        params: 其他查询参数
        include_bug_stats: 是否包含缺陷统计信息（默认 False）

        返回：按创建时间降序排序的 Story 对象列表
        """
        raw = self._get_story_by_tester(
            workspace_id=workspace_id,
            tester=tester,
            fields=fields,
            limit=limit,
            page=page,
            params=params,
        )

        stories = []
        for item in raw:
            story_id = item.get("id")
            bug_stats_obj = None
            lifetimes_obj: Optional[List[LifeTime]] = None
            if include_bug_stats and story_id:
                bug_stats_obj = self.get_story_bug_stats(workspace_id=workspace_id, story_id=story_id)
            if include_life_times and story_id:
                lifetimes_obj = self.get_story_life_times(workspace_id=workspace_id, story_id=story_id)
            stories.append(Story.from_api(item, bug_stats_obj, lifetimes_obj))

        return stories

    def get_stories_by_created_range(
        self,
        *,
        workspace_id: str | int,
        created_start: Optional[str] = None,
        created_end: Optional[str] = None,
        fields: Optional[str] = None,
        limit: Optional[int] = None,
        page: Optional[int] = None,
        include_bug_stats: bool = False,
        include_life_times: bool = False,
    ) -> List[Story]:
        """按创建时间范围查询需求列表并返回对象列表。

        时间格式为 YYYY-MM-DD。
        """
        from .time_utils import filter_items_by_time_field, compute_min_datetime_in_items, parse_datetime_bound
        logger = logging.getLogger("tapd_insight.story")

        # 仍向服务端传 created 范围表达式，但端侧强制过滤与提前停止作为兜底
        query_params: Dict[str, Any] = {}
        if created_start or created_end:
            if created_start and created_end:
                query_params["created"] = f">={created_start} <{created_end}"
            elif created_start:
                query_params["created"] = f">={created_start}"
            elif created_end:
                query_params["created"] = f"<{created_end}"

        page_no = page or 1
        page_size = limit or 200
        results: List[Story] = []

        while True:
            raw_page = self._get_stories(
                workspace_id=workspace_id,
                fields=fields,
                limit=page_size,
                page=page_no,
                order="created desc",
                params=query_params,
            )
            if not raw_page:
                break

            # 端侧过滤 raw，再构造对象，避免为越界记录做额外查询
            filtered_raw, violations = filter_items_by_time_field(
                raw_page,
                field_name="created",
                start_text=created_start,
                end_text=created_end,
            )
            if violations:
                logger.warning(f"stories_by_created_range 端侧越界过滤: {violations} 条 (page={page_no})")

            for item in filtered_raw:
                story_id = item.get("id")
                bug_stats_obj = None
                lifetimes_obj: Optional[List[LifeTime]] = None
                if include_bug_stats and story_id:
                    bug_stats_obj = self.get_story_bug_stats(workspace_id=workspace_id, story_id=story_id)
                if include_life_times and story_id:
                    lifetimes_obj = self.get_story_life_times(workspace_id=workspace_id, story_id=story_id)
                results.append(Story.from_api(item, bug_stats_obj, lifetimes_obj))

            # 提前停止：若当前页最小 created 早于下界，则无需再翻
            min_created = compute_min_datetime_in_items(raw_page, field_name="created")
            start_dt = parse_datetime_bound(created_start, default_to_start=True) if created_start else None
            if start_dt is not None and min_created is not None and min_created < start_dt:
                break
            if len(raw_page) < page_size:
                break
            page_no += 1

        return results

    @staticmethod
    def _mask_sensitive_headers(headers: Dict[str, str]) -> Dict[str, str]:
        """脱敏处理敏感请求头信息。"""
        masked = headers.copy()
        if "Authorization" in masked:
            auth_value = masked["Authorization"]
            if auth_value.startswith("Bearer "):
                token = auth_value[7:]  # 移除 "Bearer " 前缀
                if len(token) > 8:
                    masked["Authorization"] = f"Bearer {token[:4]}***{token[-4:]}"
                else:
                    masked["Authorization"] = "Bearer ***MASKED***"
        return masked

    def get_related_bugs(self, *, workspace_id: str | int, story_id: str | int | List[str | int]) -> List[StoryRelatedBug]:
        """获取需求关联的缺陷关系列表，支持单个或多个 story_id。

        参数：
            workspace_id: 工作空间ID
            story_id: 单个需求ID，或需求ID列表（最多建议200个）

        返回：
            包含 `workspace_id`、`story_id`、`bug_id` 的关系列表（扁平化）。

        参考：GET https://api.tapd.cn/stories/get_related_bugs
        文档说明支持多ID查询（以逗号分隔）。
        """
        # 兼容单个与多个 story_id
        if isinstance(story_id, (list, tuple, set)):
            ids: List[str] = [str(x) for x in story_id if str(x).strip()]
            story_id_value = ",".join(ids)
        else:
            story_id_value = str(story_id)

        query: Dict[str, Any] = {
            "workspace_id": str(workspace_id),
            "story_id": story_id_value,
        }
        qs = parse.urlencode(query, doseq=True)
        url = f"{self._related_bugs_url}?{qs}"

        headers = {
            "Authorization": self._auth.get_authorization_header(),
            "Accept": "application/json",
        }

        # 日志：请求 info，头部 debug
        logger = logging.getLogger("tapd_insight.story")
        logger.info(f"GET {url}")
        logger.debug(f"Headers: {self._mask_sensitive_headers(headers)}")

        try:
            resp = self._transport.request("GET", url, headers=headers)
            status_code = resp.status_code
            payload = resp.json()

            # 响应：状态 info，payload debug
            logger.info(f"响应 {status_code} GET {url}")
            try:
                logger.debug(json.dumps(payload, ensure_ascii=False, indent=2))
            except Exception:
                logger.debug("Response JSON: <unavailable>")
        except requests.exceptions.HTTPError as e:
            details = None
            try:
                details = resp.text[:500]
            except Exception:
                details = None
            logger.error(
                f"HTTP {status_code} 调用 TAPD get_related_bugs 接口失败"
            )
            raise TapdApiError(
                f"HTTP {status_code} 调用 TAPD get_related_bugs 接口失败",
                status_code=status_code,
                payload={"details": details},
            ) from e
        except requests.exceptions.RequestException as e:
            logging.getLogger("tapd_insight.story").error("无法连接 TAPD get_related_bugs 接口")
            raise TapdApiError("无法连接 TAPD get_related_bugs 接口", payload={"reason": str(e)}) from e

        if not isinstance(payload, dict) or payload.get("status") != 1:
            raise TapdApiError("TAPD get_related_bugs 接口返回业务状态异常", status_code=status_code, payload=payload)

        data = payload.get("data")
        if not isinstance(data, list):
            return []

        relations: List[StoryRelatedBug] = []
        for item in data:
            if not isinstance(item, dict):
                continue
            workspace_id_val = item.get("workspace_id")
            story_id_val = item.get("story_id")
            bug_id_val = item.get("bug_id")
            if workspace_id_val is None or story_id_val is None or bug_id_val is None:
                continue
            relations.append(
                StoryRelatedBug(
                    workspace_id=str(workspace_id_val),
                    story_id=str(story_id_val),
                    bug_id=str(bug_id_val),
                )
            )
        return relations

    def get_story_bug_stats(self, *, workspace_id: str | int, story_id: str | int) -> StoryBugStats:
        """获取单个需求的缺陷统计信息。

        参数:
            workspace_id: 工作空间ID
            story_id: 需求ID

        返回:
            StoryBugStats: 包含各环境缺陷数量的统计信息
        """
        # 获取需求关联的缺陷关系
        related_bugs = self.get_related_bugs(workspace_id=workspace_id, story_id=story_id)

        if not related_bugs:
            return StoryBugStats()

        # 动态导入 bug 模块，避免循环导入
        from .bug import TapdBugClient

        # 创建 bug 客户端
        bug_client = TapdBugClient(self._auth)

        # 提取缺陷 ID 列表
        bug_ids = [relation.bug_id for relation in related_bugs]

        logging.getLogger("tapd_insight.story").debug(
            f"[DEBUG] 需求 {story_id} 关联了 {len(related_bugs)} 个缺陷关系，缺陷ID数量: {len(bug_ids)}"
        )

        # 批量查询缺陷详情（查询环境、状态、修复人、报告人等字段）
        # 如果缺陷数量较多，需要分批查询
        all_bugs = []
        if len(bug_ids) <= 200:
            # 缺陷数量不超过200，一次性查询
            bugs = bug_client.get_bugs_as_objects(
                workspace_id=workspace_id,
                fields="id,custom_field_one,flows,status,fixer,reporter,title,created",  # env字段映射到custom_field_one
                limit=200,
                params={"id": ",".join(bug_ids)}
            )
            all_bugs.extend(bugs)
        else:
            # 缺陷数量超过200，分批查询
            batch_size = 200
            for i in range(0, len(bug_ids), batch_size):
                batch_ids = bug_ids[i:i + batch_size]
                bugs = bug_client.get_bugs_as_objects(
                    workspace_id=workspace_id,
                    fields="id,custom_field_one,flows,status,fixer,reporter,title,created",
                    limit=200,
                    params={"id": ",".join(batch_ids)}
                )
                all_bugs.extend(bugs)

        bugs = all_bugs

        logging.getLogger("tapd_insight.story").debug(
            f"[DEBUG] 实际查询到的缺陷数量: {len(bugs)}"
        )

        # 统计各环境缺陷数量
        stats = StoryBugStats()
        stats.bugs = bugs

        # 添加环境分类统计调试
        env_debug = {"PROD": 0, "PROD_VERIFY": 0, "UAT": 0, "FAT": 0, "None": 0}

        for bug in bugs:
            # 统计有效缺陷（非无效缺陷）
            is_valid = not bug.is_invalid()
            if is_valid:
                stats.valid_bugs += 1
            else:
                stats.invalid_bugs += 1

            # 按环境统计
            environment = bug.get_environment()
            if is_valid:
                if environment == "PROD":
                    stats.prod_bugs += 1
                    env_debug["PROD"] += 1
                elif environment == "PROD_VERIFY":
                    stats.prod_verify_bugs += 1
                    env_debug["PROD_VERIFY"] += 1
                elif environment == "UAT":
                    stats.uat_bugs += 1
                    env_debug["UAT"] += 1
                elif environment == "FAT":
                    stats.fat_bugs += 1
                    env_debug["FAT"] += 1
                else:
                    # 没有环境标识的归类到 FAT 环境
                    stats.fat_bugs += 1
                    env_debug["None"] += 1
                    logging.getLogger("tapd_insight.story").debug(
                        f"[DEBUG] 缺陷 {bug.id} 环境字段为空或未识别: env='{bug.env}', environment='{environment}'"
                    )

            # 分组统计：fixer 与 reporter
            if bug.fixer:
                fixer_key = bug.fixer.strip()
                if fixer_key:
                    agg = stats.fixer_aggregates.get(fixer_key)
                    if agg is None:
                        agg = FixerBugAggregate()
                        stats.fixer_aggregates[fixer_key] = agg
                    if is_valid:
                        agg.total += 1
                        # reopen 次数
                        try:
                            rt = int(getattr(bug, "reopen_times", 0) or 0)
                        except Exception:
                            rt = 0
                        if rt == 1:
                            agg.reopen_once += 1
                        elif rt > 1:
                            agg.reopen_multi += 1
                        # 有效缺陷的环境分类
                        if environment == "PROD":
                            agg.prod_bugs += 1
                        elif environment == "PROD_VERIFY":
                            agg.prod_verify_bugs += 1
                        elif environment == "UAT":
                            agg.uat_bugs += 1
                        elif environment == "FAT" or environment is None:
                            agg.fat_bugs += 1
            if bug.reporter:
                reporter_key = bug.reporter.strip()
                if reporter_key:
                    agg = stats.reporter_counts.get(reporter_key)
                    if agg is None:
                        agg = ReporterBugAggregate()
                        stats.reporter_counts[reporter_key] = agg
                    # 总缺陷数
                    agg.total += 1
                    # 有效缺陷数
                    if is_valid:
                        agg.valid_bugs += 1

        logging.getLogger("tapd_insight.story").debug(f"[DEBUG] 环境分类详情: {env_debug}")

        logging.getLogger("tapd_insight.story").debug(
            f"[DEBUG] 缺陷统计结果: FAT={stats.fat_bugs}, UAT={stats.uat_bugs}, PROD={stats.prod_bugs}, PROD_VERIFY={stats.prod_verify_bugs}, VALID={stats.valid_bugs}, INVALID={stats.invalid_bugs}"
        )

        return stats


def demo_get_stories_as_objects() -> None:
    from .auth import TapdClientCredentialsAuth
    from .config import load_tapd_config

    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(client_id=cfg.client_id, client_secret=cfg.client_secret)
    client = TapdStoryClient(auth)

    stories = client.get_stories_as_objects(workspace_id=cfg.workspace_id, limit=5, page=1)
    logging.getLogger("tapd_insight.story").info(f"get_stories_as_objects: {len(stories)} 条")


def demo_get_story_by_id_as_object() -> None:
    from .auth import TapdClientCredentialsAuth
    from .config import load_tapd_config

    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(client_id=cfg.client_id, client_secret=cfg.client_secret)
    client = TapdStoryClient(auth)
    sid = "1148986738001067214"
    one = client.get_story_by_id_as_object(workspace_id=cfg.workspace_id, story_id=sid, include_bug_stats=True)
    logging.getLogger("tapd_insight.story").info(
        f"get_story_by_id_as_object: 有父需求?={one.has_parent()} data={one}"
    )


def demo_get_related_bugs_and_stats() -> None:
    from .auth import TapdClientCredentialsAuth
    from .config import load_tapd_config

    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(client_id=cfg.client_id, client_secret=cfg.client_secret)
    client = TapdStoryClient(auth)

    stories = client.get_stories_as_objects(workspace_id=cfg.workspace_id, limit=1, page=1)
    if not stories:
        logging.getLogger("tapd_insight.story").warning("get_related_bugs/get_story_bug_stats: 无数据")
        return
    sid = stories[0].id
    rel = client.get_related_bugs(workspace_id=cfg.workspace_id, story_id=sid)
    stats = client.get_story_bug_stats(workspace_id=cfg.workspace_id, story_id=sid)
    logging.getLogger("tapd_insight.story").info(
        f"get_related_bugs: {len(rel)} 条; get_story_bug_stats: 有效={stats.valid_bugs}"
    )


def demo_get_story_by_tester_as_object() -> None:
    from .auth import TapdClientCredentialsAuth
    from .config import load_tapd_config

    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(client_id=cfg.client_id, client_secret=cfg.client_secret)
    client = TapdStoryClient(auth)

    stories = client.get_stories_as_objects(workspace_id=cfg.workspace_id, limit=1, page=1)
    tester = None
    if stories:
        first = stories[0]
        if getattr(first, "testers", None):
            if isinstance(first.testers, list) and first.testers:
                tester = str(first.testers[0])
        if not tester and getattr(first, "test_lead", None):
            tester = str(first.test_lead)
    if not tester:
        logging.getLogger("tapd_insight.story").warning("get_story_by_tester_as_object: 未找到示例测试人员，跳过")
        return
    result = client.get_story_by_tester_as_object(workspace_id=cfg.workspace_id, tester=tester, limit=5, page=1)
    logging.getLogger("tapd_insight.story").info(
        f"get_story_by_tester_as_object: tester={tester}, count={len(result)}"
    )

def demo_get_story_life_times() -> None:
    """Demo：查询指定需求的状态流转时间线。"""
    from .auth import TapdClientCredentialsAuth
    from .config import load_tapd_config
    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(client_id=cfg.client_id, client_secret=cfg.client_secret)
    client = TapdStoryClient(auth)
    sid = "1148986738001066302"
    story = client.get_story_by_id_as_object(
        workspace_id=cfg.workspace_id,
        story_id=sid,
        include_life_times=True,
    )
    if not story:
        logging.getLogger("tapd_insight.story").warning("demo_get_story_life_times: 未查询到需求")
        return
    logging.getLogger("tapd_insight.story").info(
        f"demo_get_story_life_times: story_id={sid}, lifetimes={len(story.lifetimes)}"
    )
    for lt in story.lifetimes:
        logging.getLogger("tapd_insight.story").info(json.dumps({
            "id": lt.id,
            "status": lt.status,
            "owner": lt.owner,
            "created": lt.created,
            "begin_date": lt.begin_date,
            "end_date": lt.end_date,
            "time_cost_hours": lt.time_cost_hours,
            "operator": lt.operator,
            "change_from": lt.change_from,
        }, ensure_ascii=False))

def demo_get_stories_by_created_range() -> None:
    """Demo：按创建时间范围查询需求列表。"""
    from .auth import TapdClientCredentialsAuth
    from .config import load_tapd_config

    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(client_id=cfg.client_id, client_secret=cfg.client_secret)
    client = TapdStoryClient(auth)

    # 查询最近7天创建的需求
    from datetime import datetime, timedelta
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")

    # 分批次查询所有需求
    all_stories = []
    page = 1
    limit = 200

    while True:
        stories = client.get_stories_by_created_range(
            workspace_id=cfg.workspace_id,
            created_start=start_date,
            created_end=end_date,
            limit=limit,
            page=page
        )

        if not stories:
            # 当前页没有数据，结束查询
            break

        all_stories.extend(stories)
        logging.getLogger("tapd_insight.story").info(f"第 {page} 页查询到 {len(stories)} 个需求")

        if len(stories) < limit:
            # 当前页数据不足limit个，说明已经是最后一页
            break

        page += 1

    logging.getLogger("tapd_insight.story").info(f"demo_get_stories_by_created_range: 时间范围 {start_date} 到 {end_date}")
    logging.getLogger("tapd_insight.story").info(f"总共查询到 {len(all_stories)} 个需求")

    for story in all_stories:
        logging.getLogger("tapd_insight.story").info(f"需求ID: {story.id}, 名称: {story.name}, 创建时间: {story.created}")
        if story.bug_stats:
            logging.getLogger("tapd_insight.story").info(f"  缺陷统计: 有效缺陷 {story.bug_stats.valid_bugs} 个, 无效缺陷 {story.bug_stats.invalid_bugs} 个")
        if story.lifetimes:
            logging.getLogger("tapd_insight.story").info(f"  状态流转记录: {len(story.lifetimes)} 条")


def demo_get_stories_as_objects_yesterday_to_today() -> None:
    """Demo：调用 get_stories_as_objects 查询昨天到今天创建的需求，按创建时间降序。

    依据官方文档中 created 字段的时间范围查询能力，构造形如
    ">YYYY-MM-DD <YYYY-MM-DD" 的查询表达式，其中下界为昨天，上界为今天。
    """
    from .auth import TapdClientCredentialsAuth
    from .config import load_tapd_config
    from datetime import datetime, timedelta

    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(client_id=cfg.client_id, client_secret=cfg.client_secret)
    client = TapdStoryClient(auth)

    # 使用明确的时间边界，并以两个 created 参数表达范围：
    # created>=昨天 00:00:00 且 created<明天 00:00:00
    # 这样可以包含“昨天到今天（截至当前）”创建的所有需求，且避免跨天误差
    now = datetime.now()
    today = now.replace(hour=0, minute=0, second=0, microsecond=0)
    yesterday = today - timedelta(days=1)
    tomorrow = today + timedelta(days=1)

    yesterday_str = yesterday.strftime("%Y-%m-%d %H:%M:%S")
    tomorrow_str = tomorrow.strftime("%Y-%m-%d %H:%M:%S")

    # 使用单个 created 参数写入范围表达式，避免服务端仅采纳最后一个 created 参数
    query_params = {"created": f">={yesterday_str} <{tomorrow_str}"}

    page = 1
    limit = 200
    total = 0

    while True:
        stories = client.get_stories_as_objects(
            workspace_id=cfg.workspace_id,
            limit=limit,
            page=page,
            order="created desc",
            params=query_params,
        )

        if not stories:
            break

        total += len(stories)
        # 本地校验与过滤：仅输出区间内数据；若发现越界则告警
        from datetime import datetime as _dt
        def _parse_created(v: Optional[str]) -> Optional[datetime]:
            if not v:
                return None
            v = v.strip()
            for fmt in ("%Y-%m-%d %H:%M:%S", "%Y-%m-%d %H:%M", "%Y-%m-%d"):
                try:
                    return _dt.strptime(v, fmt)
                except Exception:
                    continue
            return None
        start_dt = _dt.strptime(yesterday_str, "%Y-%m-%d %H:%M:%S")
        end_dt = _dt.strptime(tomorrow_str, "%Y-%m-%d %H:%M:%S")
        filtered = []
        violations = 0
        sample = None
        created_list: List[datetime] = []
        for s in stories:
            cd = _parse_created(s.created)
            if cd is not None:
                created_list.append(cd)
            if cd is not None and (start_dt <= cd < end_dt):
                filtered.append(s)
            else:
                violations += 1
                if sample is None:
                    sample = (s.id, s.created)

        for s in filtered:
            logging.getLogger("tapd_insight.story").info(f"需求ID: {s.id}, 名称: {s.name}, 创建时间: {s.created}")

        if violations and not filtered:
            logging.getLogger("tapd_insight.story").warning(
                f"created 越界记录: {violations} 条，示例 id={sample[0]} created={sample[1]}"
            )

        # 若当前页中最小 created 已早于下界，说明后续页更旧，可停止翻页
        if created_list:
            min_cd = min(created_list)
            if min_cd < start_dt:
                break

        if len(stories) < limit:
            break

        page += 1

    logging.getLogger("tapd_insight.story").info(
        f"demo_get_stories_as_objects_yesterday_to_today: 时间范围 {yesterday_str} 到 {tomorrow_str}(开区间)，共 {total} 条"
    )


def demo_get_story_by_tester_in_created_range() -> None:
    """Demo：按测试人员与创建时间范围查询需求（端侧过滤 + 提前停止翻页）。

    逻辑：
    1) 先探测一个测试人员名称作为演示对象
    2) 按 [昨天00:00:00, 明天00:00:00) 时间窗口分页查询 tester 负责的需求
    3) 对每页结果做端侧时间过滤，并在最小 created 早于下界时提前停止翻页
    """
    from .auth import TapdClientCredentialsAuth
    from .config import load_tapd_config
    from datetime import datetime, timedelta
    from .time_utils import (
        filter_items_by_time_field,
        compute_min_datetime_in_items,
        parse_datetime_bound,
    )

    logger = logging.getLogger("tapd_insight.story")

    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(client_id=cfg.client_id, client_secret=cfg.client_secret)
    client = TapdStoryClient(auth)

    # 1) 直接指定测试人员
    tester = "毛浩"

    # 2) 构造时间窗口：昨天00:00:00 ~ 明天00:00:00（开区间右）
    now = datetime.now()
    today = now.replace(hour=0, minute=0, second=0, microsecond=0)
    yesterday = today - timedelta(days=1)
    tomorrow = today + timedelta(days=1)
    start_text = yesterday.strftime("%Y-%m-%d %H:%M:%S")
    end_text = tomorrow.strftime("%Y-%m-%d %H:%M:%S")
    start_dt = parse_datetime_bound(start_text, default_to_start=True)

    # 3) 分页查询 + 端侧过滤
    page = 1
    limit = 200
    total = 0
    while True:
        page_items = client.get_story_by_tester_as_object(
            workspace_id=cfg.workspace_id,
            tester=tester,
            limit=limit,
            page=page,
            params={"created": f">={start_text} <{end_text}"},
        )
        if not page_items:
            break

        filtered, violations = filter_items_by_time_field(
            page_items,
            field_name="created",
            start_text=start_text,
            end_text=end_text,
        )
        if violations:
            logger.warning(f"tester_in_created_range 端侧越界过滤: {violations} 条 (page={page})")

        for s in filtered:
            total += 1
            logging.getLogger("tapd_insight.story").info(f"需求ID: {s.id}, 名称: {s.name}, 创建时间: {s.created}")

        # 提前停止：若当前页最小 created 早于下界，无需继续
        min_cd = compute_min_datetime_in_items(page_items, field_name="created")
        if start_dt is not None and min_cd is not None and min_cd < start_dt:
            break
        if len(page_items) < limit:
            break
        page += 1

    logging.getLogger("tapd_insight.story").info(
        f"demo_get_story_by_tester_in_created_range: tester={tester} 时间范围 {start_text} 到 {end_text}(开区间)，共 {total} 条"
    )

def demo_get_related_bugs_multi_story() -> None:
    """Demo：批量查询多个需求的关联缺陷关系，并打印每个需求的缺陷数量。"""
    from .auth import TapdClientCredentialsAuth
    from .config import load_tapd_config

    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(client_id=cfg.client_id, client_secret=cfg.client_secret)
    client = TapdStoryClient(auth)

    story_ids = ["1148986738001063936", "1148986738001065658"]

    relations = client.get_related_bugs(workspace_id=cfg.workspace_id, story_id=story_ids)
    logging.getLogger("tapd_insight.story").info(
        f"demo_get_related_bugs_multi_story: 请求 story_ids={story_ids}，返回关系 {len(relations)} 条"
    )

    counts: Dict[str, int] = {}
    for r in relations:
        counts[r.story_id] = counts.get(r.story_id, 0) + 1
    for sid in story_ids:
        logging.getLogger("tapd_insight.story").info(f"  story_id={sid} -> bug_count={counts.get(sid, 0)}")

def demo_get_stories_by_ids_as_objects() -> None:
    """Demo：演示按多个需求 ID 批量查询并返回 Story 对象列表。"""
    from .auth import TapdClientCredentialsAuth
    from .config import load_tapd_config

    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(client_id=cfg.client_id, client_secret=cfg.client_secret)
    client = TapdStoryClient(auth)

    story_ids = ["1148986738001067313", "1148986738001067287"]
    stories = client.get_stories_by_ids_as_objects(
        workspace_id=cfg.workspace_id,
        story_ids=story_ids,
        include_bug_stats=True,
        include_life_times=True,
    )
    logging.getLogger("tapd_insight.story").info(
        f"demo_get_stories_by_ids_as_objects: story_ids={story_ids}, stories={len(stories)}, stories={stories}"
    )

if __name__ == "__main__":
    from tapd_insight.config import setup_tapd_logging
    setup_tapd_logging("INFO")  # 想看响应体就改为 "DEBUG"
    # demo_get_stories_as_objects()
    demo_get_story_by_id_as_object()
    # demo_get_stories_by_created_range()
    # demo_get_related_bugs_and_stats()
    # demo_get_story_by_tester_as_object()
    # demo_get_story_life_times()
    # demo_get_stories_as_objects_yesterday_to_today()
    # demo_get_story_by_tester_in_created_range()
    # demo_get_related_bugs_multi_story()
    # demo_get_stories_by_ids_as_objects()
