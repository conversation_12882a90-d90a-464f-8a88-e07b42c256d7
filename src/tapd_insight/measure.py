"""
TAPD 度量：状态流转时间查询（life_times）。

参考文档：
https://open.tapd.cn/document/api-doc/API%E6%96%87%E6%A1%A3/api_reference/measure/get_life_times.html

实现说明：
- 基于 `TapdClientCredentialsAuth` 提供的 Bearer Token 鉴权
- 通过统一传输层 `TapdApiTransport` 发起 HTTP 请求
- 对返回进行结构化，输出 `LifeTime` 对象列表
- 支持基本的分页与排序参数；默认按 `created desc` 排序
"""

from __future__ import annotations

import json
from dataclasses import dataclass
from typing import Any, Dict, List, Optional
from urllib import parse

import requests

from .auth import TapdClientCredentialsAuth
from .transport import TapdApiTransport


# https://open.tapd.cn/document/api-doc/API%E6%96%87%E6%A1%A3/api_reference/measure/get_life_times.html
DEFAULT_LIFE_TIMES_URL = "https://api.tapd.cn/life_times"


class TapdMeasureApiError(Exception):
    """TAPD 度量相关 API 调用错误。"""

    def __init__(self, message: str, *, status_code: Optional[int] = None, payload: Optional[Dict[str, Any]] = None) -> None:
        super().__init__(message)
        self.status_code = status_code
        self.payload = payload


@dataclass
class LifeTime:
    """状态流转时间实体。

    字段含义参考官方文档。
    """

    id: str
    workspace_id: str
    entity_type: str
    entity_id: str
    status: Optional[str]
    owner: Optional[str]
    is_repeated: Optional[bool]
    begin_date: Optional[str]
    end_date: Optional[str]
    time_cost_hours: Optional[float]
    created: Optional[str]
    operator: Optional[str]
    change_from: Optional[str] = None

    @staticmethod
    def from_api(d: Dict[str, Any]) -> "LifeTime":
        def to_str(v: Any) -> Optional[str]:
            if v is None:
                return None
            return str(v)

        def to_bool(v: Any) -> Optional[bool]:
            if v is None:
                return None
            if isinstance(v, bool):
                return v
            s = str(v).strip().lower()
            if s in {"1", "true", "yes", "y"}:
                return True
            if s in {"0", "false", "no", "n"}:
                return False
            try:
                return bool(int(s))
            except Exception:
                return None

        def to_float(v: Any) -> Optional[float]:
            if v is None or v == "":
                return None
            try:
                return float(v)
            except Exception:
                return None

        return LifeTime(
            id=str(d.get("id", "")),
            workspace_id=str(d.get("workspace_id", "")),
            entity_type=str(d.get("entity_type", "")),
            entity_id=str(d.get("entity_id", "")),
            status=to_str(d.get("status")),
            owner=to_str(d.get("owner")),
            is_repeated=to_bool(d.get("is_repeated")),
            begin_date=to_str(d.get("begin_date")),
            end_date=to_str(d.get("end_date")),
            time_cost_hours=to_float(d.get("time_cost")),
            created=to_str(d.get("created")),
            operator=to_str(d.get("operator")),
            change_from=to_str(d.get("change_from")),
        )


class TapdMeasureClient:
    """TAPD 度量客户端：提供 life_times 查询能力。"""

    def __init__(
        self,
        auth: TapdClientCredentialsAuth,
        *,
        life_times_url: str = DEFAULT_LIFE_TIMES_URL,
        request_timeout_seconds: float = 10.0,
    ) -> None:
        self._auth = auth
        self._life_times_url = life_times_url
        self._timeout = request_timeout_seconds
        self._transport = TapdApiTransport(request_timeout_seconds=request_timeout_seconds)

    @staticmethod
    def _mask_sensitive_headers(headers: Dict[str, str]) -> Dict[str, str]:
        masked = headers.copy()
        if "Authorization" in masked:
            auth_value = masked["Authorization"]
            if auth_value.startswith("Bearer "):
                token = auth_value[7:]
                if len(token) > 8:
                    masked["Authorization"] = f"Bearer {token[:4]}***{token[-4:]}"
                else:
                    masked["Authorization"] = "Bearer ***MASKED***"
        return masked

    def get_life_times(
        self,
        *,
        workspace_id: str | int,
        entity_type: str,
        entity_id: str | int,
        limit: Optional[int] = None,
        page: Optional[int] = None,
        order: Optional[str] = "created desc",
        fields: Optional[str] = None,
        extra_params: Optional[Dict[str, Any]] = None,
    ) -> List[LifeTime]:
        """查询状态流转时间列表。

        仅做一次请求，不做自动翻页。默认按 `created desc` 排序。
        """
        query: Dict[str, Any] = {}
        query.update(extra_params or {})
        query["workspace_id"] = str(workspace_id)
        query["entity_type"] = entity_type
        query["entity_id"] = str(entity_id)
        if fields:
            query["fields"] = fields
        if limit is not None:
            query["limit"] = int(limit)
        if page is not None:
            query["page"] = int(page)
        if order:
            query["order"] = order

        qs = parse.urlencode(query, doseq=True)
        url = f"{self._life_times_url}?{qs}" if qs else self._life_times_url

        headers = {
            "Authorization": self._auth.get_authorization_header(),
            "Accept": "application/json",
        }

        print(f"[MEASURE] GET {url}")
        print(f"[MEASURE] Headers: {self._mask_sensitive_headers(headers)}")

        try:
            resp = self._transport.request("GET", url, headers=headers)
            status_code = resp.status_code
            payload = resp.json()
            print(f"[MEASURE] Response Status: {status_code}")
            print(f"[MEASURE] Response Payload: {json.dumps(payload, ensure_ascii=False, indent=2)}")
        except requests.exceptions.HTTPError as e:
            details = None
            try:
                details = resp.text[:500]
            except Exception:
                details = None
            raise TapdMeasureApiError(
                f"HTTP {status_code} 调用 TAPD life_times 接口失败",
                status_code=status_code,
                payload={"details": details},
            ) from e
        except requests.exceptions.RequestException as e:
            raise TapdMeasureApiError("无法连接 TAPD life_times 接口", payload={"reason": str(e)}) from e

        if not isinstance(payload, dict) or payload.get("status") != 1:
            raise TapdMeasureApiError("TAPD life_times 接口返回业务状态异常", status_code=status_code, payload=payload)

        data = payload.get("data")
        if not isinstance(data, list):
            return []

        result: List[LifeTime] = []
        for item in data:
            if not isinstance(item, dict):
                continue
            life_dict = item.get("LifeTime")
            if isinstance(life_dict, dict):
                result.append(LifeTime.from_api(life_dict))
        return result


def demo_get_story_life_times() -> None:
    """Demo：查询指定需求的状态流转时间，按创建时间降序。"""
    from .config import load_tapd_config

    cfg = load_tapd_config()
    auth = TapdClientCredentialsAuth(client_id=cfg.client_id, client_secret=cfg.client_secret)
    client = TapdMeasureClient(auth)

    story_id = "1148986738001065219"
    life_times = client.get_life_times(
        workspace_id=cfg.workspace_id,
        entity_type="story",
        entity_id=story_id,
        order="created desc",
        limit=200,
        page=1,
    )
    print(f"get_life_times: story_id={story_id}, count={len(life_times)}")
    for lt in life_times:
        print(
            json.dumps(
                {
                    "id": lt.id,
                    "status": lt.status,
                    "owner": lt.owner,
                    "created": lt.created,
                    "begin_date": lt.begin_date,
                    "end_date": lt.end_date,
                    "time_cost_hours": lt.time_cost_hours,
                    "operator": lt.operator,
                    "change_from": lt.change_from,
                },
                ensure_ascii=False,
            )
        )


if __name__ == "__main__":
    demo_get_story_life_times()


