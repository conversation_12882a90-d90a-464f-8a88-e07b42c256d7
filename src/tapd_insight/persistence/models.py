"""
SQLAlchemy ORM 模型定义。
"""

from __future__ import annotations

from sqlalchemy import Column, String, Integer, BigInteger, Text, DateTime, DECIMAL, Boolean, TIMESTAMP, text
from sqlalchemy.orm import Mapped, mapped_column
from .db import Base


class TapdStory(Base):
    __tablename__ = "tapd_stories"

    workspace_id: Mapped[str] = mapped_column(String(32), primary_key=True)
    story_id: Mapped[str] = mapped_column(String(32), primary_key=True)
    name: Mapped[str | None] = mapped_column(String(512))
    priority_label: Mapped[str | None] = mapped_column(String(64))
    status: Mapped[str | None] = mapped_column(String(64))
    v_status: Mapped[str | None] = mapped_column(String(64))
    module: Mapped[str | None] = mapped_column(String(128))
    size: Mapped[float | None] = mapped_column(DECIMAL(10, 2))
    owner: Mapped[str | None] = mapped_column(String(128))
    tech_lead: Mapped[str | None] = mapped_column(String(128))
    test_lead: Mapped[str | None] = mapped_column(String(128))
    product_manager: Mapped[str | None] = mapped_column(String(128))
    iteration_id: Mapped[str | None] = mapped_column(String(32))
    category_id: Mapped[str | None] = mapped_column(String(32))
    ancestor_id: Mapped[str | None] = mapped_column(String(32))
    parent_id: Mapped[str | None] = mapped_column(String(32))
    tech_design_start: Mapped[str | None] = mapped_column(DateTime)
    tech_design_end: Mapped[str | None] = mapped_column(DateTime)
    dev_start: Mapped[str | None] = mapped_column(DateTime)
    test_start: Mapped[str | None] = mapped_column(DateTime)
    created: Mapped[str | None] = mapped_column(DateTime)
    review_passed: Mapped[str | None] = mapped_column(DateTime)
    begin: Mapped[str | None] = mapped_column(DateTime)
    due: Mapped[str | None] = mapped_column(DateTime)
    planned_test_date: Mapped[str | None] = mapped_column(DateTime)
    actual_test_date: Mapped[str | None] = mapped_column(DateTime)
    planned_test_complete_date: Mapped[str | None] = mapped_column(DateTime)
    actual_test_complete_date: Mapped[str | None] = mapped_column(DateTime)
    planned_uat_complete_date: Mapped[str | None] = mapped_column(DateTime)
    actual_uat_complete_date: Mapped[str | None] = mapped_column(DateTime)
    planned_release_date: Mapped[str | None] = mapped_column(DateTime)
    effort: Mapped[float | None] = mapped_column(DECIMAL(10, 2))
    effort_completed: Mapped[float | None] = mapped_column(DECIMAL(10, 2))
    remain: Mapped[float | None] = mapped_column(DECIMAL(10, 2))
    exceed: Mapped[float | None] = mapped_column(DECIMAL(10, 2))
    created_at: Mapped[str] = mapped_column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'))
    updated_at: Mapped[str] = mapped_column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))


class TapdStoryTester(Base):
    __tablename__ = "tapd_story_testers"

    workspace_id: Mapped[str] = mapped_column(String(32), primary_key=True)
    story_id: Mapped[str] = mapped_column(String(32), primary_key=True)
    tester: Mapped[str] = mapped_column(String(128), primary_key=True)


class TapdStoryDeveloper(Base):
    __tablename__ = "tapd_story_developers"

    workspace_id: Mapped[str] = mapped_column(String(32), primary_key=True)
    story_id: Mapped[str] = mapped_column(String(32), primary_key=True)
    developer: Mapped[str] = mapped_column(String(128), primary_key=True)


class TapdStoryLifetime(Base):
    __tablename__ = "tapd_story_lifetimes"

    workspace_id: Mapped[str] = mapped_column(String(32), primary_key=True)
    story_id: Mapped[str] = mapped_column(String(32), primary_key=True)
    lifetime_id: Mapped[str] = mapped_column(String(64), primary_key=True)
    status: Mapped[str | None] = mapped_column(String(64))
    owner: Mapped[str | None] = mapped_column(String(128))
    created: Mapped[str | None] = mapped_column(DateTime)
    begin_date: Mapped[str | None] = mapped_column(DateTime)
    end_date: Mapped[str | None] = mapped_column(DateTime)
    time_cost_hours: Mapped[float | None] = mapped_column(DECIMAL(12, 2))
    operator: Mapped[str | None] = mapped_column(String(128))
    change_from: Mapped[str | None] = mapped_column(String(64))


class TapdBug(Base):
    __tablename__ = "tapd_bugs"

    workspace_id: Mapped[str] = mapped_column(String(32), primary_key=True)
    bug_id: Mapped[str] = mapped_column(String(32), primary_key=True)
    iteration_id: Mapped[str | None] = mapped_column(String(32))
    story_id: Mapped[str | None] = mapped_column(String(32))
    title: Mapped[str | None] = mapped_column(String(512))
    priority: Mapped[str | None] = mapped_column(String(64))
    priority_label: Mapped[str | None] = mapped_column(String(64))
    severity: Mapped[str | None] = mapped_column(String(64))
    status: Mapped[str | None] = mapped_column(String(64))
    current_owner: Mapped[str | None] = mapped_column(String(128))
    reporter: Mapped[str | None] = mapped_column(String(128))
    created: Mapped[str | None] = mapped_column(DateTime)
    de: Mapped[str | None] = mapped_column(String(128))
    te: Mapped[str | None] = mapped_column(String(128))
    bugtype: Mapped[str | None] = mapped_column(String(128))
    fixer: Mapped[str | None] = mapped_column(String(128))
    closer: Mapped[str | None] = mapped_column(String(128))
    env_raw: Mapped[str | None] = mapped_column(String(128))
    env_normalized: Mapped[str | None] = mapped_column(String(32))
    flows: Mapped[str | None] = mapped_column(Text)
    deadline: Mapped[str | None] = mapped_column(DateTime)
    has_reopened: Mapped[bool] = mapped_column(Boolean, default=False)
    reopen_times: Mapped[int] = mapped_column(Integer, default=0)
    is_valid: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[str] = mapped_column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'))
    updated_at: Mapped[str] = mapped_column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))


class TapdIteration(Base):
    __tablename__ = "tapd_iterations"

    workspace_id: Mapped[str] = mapped_column(String(32), primary_key=True)
    iteration_id: Mapped[str] = mapped_column(String(32), primary_key=True)
    name: Mapped[str | None] = mapped_column(String(512))
    startdate: Mapped[str | None] = mapped_column(DateTime)
    enddate: Mapped[str | None] = mapped_column(DateTime)
    status: Mapped[str | None] = mapped_column(String(64))
    creator: Mapped[str | None] = mapped_column(String(128))
    created: Mapped[str | None] = mapped_column(DateTime)
    modified: Mapped[str | None] = mapped_column(DateTime)
    completed: Mapped[str | None] = mapped_column(DateTime)
    description: Mapped[str | None] = mapped_column(Text)
    created_at: Mapped[str] = mapped_column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'))
    updated_at: Mapped[str] = mapped_column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))


class TapdSyncCheckpoint(Base):
    __tablename__ = "tapd_sync_checkpoints"

    id: Mapped[int] = mapped_column(BigInteger, primary_key=True, autoincrement=True)
    entity: Mapped[str] = mapped_column(String(32))
    workspace_id: Mapped[str] = mapped_column(String(32))
    last_created: Mapped[str | None] = mapped_column(DateTime)
    last_modified: Mapped[str | None] = mapped_column(DateTime)
    last_id: Mapped[str | None] = mapped_column(String(64))
    updated_at: Mapped[str] = mapped_column(TIMESTAMP, server_default=text('CURRENT_TIMESTAMP'), server_onupdate=text('CURRENT_TIMESTAMP'))


