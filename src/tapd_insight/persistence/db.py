"""
SQLAlchemy 会话与引擎管理。

说明：
- 不自动安装依赖，请自行安装 sqlalchemy、pymysql
- 通过传入数据库 URL 创建引擎，例如：
  mysql+pymysql://user:password@127.0.0.1:3306/tapd_insight
"""

from __future__ import annotations

from typing import Iterator
from contextlib import contextmanager
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, DeclarativeBase


class Base(DeclarativeBase):
    pass


def create_sqlalchemy_engine(db_url: str):
    """创建 SQLAlchemy Engine。"""
    engine = create_engine(db_url, pool_pre_ping=True, future=True)
    return engine


def create_session_factory(db_url: str):
    """基于数据库 URL 创建 SessionFactory。"""
    engine = create_sqlalchemy_engine(db_url)
    return sessionmaker(bind=engine, autoflush=False, autocommit=False, expire_on_commit=False, future=True)


@contextmanager
def session_scope(Session):
    """上下文管理的会话范围，用于自动提交或回滚。"""
    session = Session()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


