"""
通用时间解析与端侧过滤工具。

提供灵活的时间字符串解析、范围判断与列表过滤能力，
用于在服务端过滤不可靠时进行端侧强制过滤，并支持分页提前停止策略的判断。
"""

from __future__ import annotations

from datetime import datetime
from typing import Iterable, List, Optional, Tuple, Any, Dict


_DT_FORMATS = (
    "%Y-%m-%d %H:%M:%S",
    "%Y-%m-%d %H:%M",
    "%Y-%m-%d",
)


def parse_datetime_flexible(value: Optional[str]) -> Optional[datetime]:
    """尽可能解析多种常见格式的时间字符串。

    支持格式：
    - YYYY-MM-DD HH:MM:SS
    - YYYY-MM-DD HH:MM
    - YYYY-MM-DD
    解析失败返回 None。
    """
    if value is None:
        return None
    text = str(value).strip()
    if not text:
        return None
    for fmt in _DT_FORMATS:
        try:
            return datetime.strptime(text, fmt)
        except Exception:
            continue
    return None


def parse_datetime_bound(value: Optional[str], *, default_to_start: bool) -> Optional[datetime]:
    """解析边界时间，若只给出日期，补齐到边界端。

    当 default_to_start 为 True，且只包含日期时，补 00:00:00；
    当 default_to_start 为 False，且只包含日期时，补 23:59:59。
    未能解析返回 None。
    """
    if value is None:
        return None
    text = str(value).strip()
    if not text:
        return None
    dt = parse_datetime_flexible(text)
    if dt is None:
        return None
    # 若格式为日期级别，进行边界补齐
    if len(text) == 10 and text.count("-") == 2:
        if default_to_start:
            return datetime(dt.year, dt.month, dt.day, 0, 0, 0)
        return datetime(dt.year, dt.month, dt.day, 23, 59, 59)
    return dt


def is_in_range(
    dt_text: Optional[str],
    *,
    start_text: Optional[str] = None,
    end_text: Optional[str] = None,
    inclusive_start: bool = True,
    exclusive_end: bool = True,
) -> bool:
    """判断给定时间字符串是否落在区间内。

    区间语义：左闭右开 [start, end)。
    当 start_text 或 end_text 为空时，按单边界判断。
    无法解析目标时间时返回 False。
    """
    target = parse_datetime_flexible(dt_text)
    if target is None:
        return False
    if start_text:
        start_dt = parse_datetime_bound(start_text, default_to_start=True)
        if start_dt is not None:
            if inclusive_start:
                if target < start_dt:
                    return False
            else:
                if target <= start_dt:
                    return False
    if end_text:
        end_dt = parse_datetime_bound(end_text, default_to_start=False)
        if end_dt is not None:
            if exclusive_end:
                if target >= end_dt:
                    return False
            else:
                if target > end_dt:
                    return False
    return True


def filter_items_by_time_field(
    items: Iterable[Any],
    *,
    field_name: str,
    start_text: Optional[str] = None,
    end_text: Optional[str] = None,
) -> Tuple[List[Any], int]:
    """按对象属性或字典键进行端侧时间范围过滤。

    返回 (filtered_list, violations_count)。
    无法解析时间的记录计入越界统计。
    """
    result: List[Any] = []
    violations = 0
    for item in items:
        value = None
        if isinstance(item, dict):
            value = item.get(field_name)
        else:
            value = getattr(item, field_name, None)
        if is_in_range(value, start_text=start_text, end_text=end_text):
            result.append(item)
        else:
            violations += 1
    return result, violations


def compute_min_datetime_in_items(items: Iterable[Any], *, field_name: str) -> Optional[datetime]:
    """计算给定列表中字段的最小时间，用于判断是否可以提前停止翻页。"""
    min_dt: Optional[datetime] = None
    for item in items:
        value = None
        if isinstance(item, dict):
            value = item.get(field_name)
        else:
            value = getattr(item, field_name, None)
        dt = parse_datetime_flexible(value)
        if dt is None:
            continue
        if min_dt is None or dt < min_dt:
            min_dt = dt
    return min_dt


