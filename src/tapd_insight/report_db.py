"""
基于数据库的报表模块。

说明与约定
1) 不改动 iteration.py 与 tester_report.py，二者继续保留 API 直连版本
2) 本模块以 MySQL 中的持久化表为唯一数据源，生成等价口径的报表
3) 依赖项不自动安装，请手动安装：
   - SQLAlchemy, PyMySQL
   - pandas, openpyxl（仅当需要导出 Excel 时）
   示例安装命令（请手动执行）：
   uv add "SQLAlchemy>=2.0" "pymysql>=1.1" "pandas>=2.0" "openpyxl>=3.1"

入口用法示例
from tapd_insight.persistence.db import create_session_factory, session_scope
from tapd_insight.report_db import DbIterationReportGenerator, DbTesterReportGenerator

Session = create_session_factory("mysql+pymysql://user:pwd@127.0.0.1:3306/tapd_insight")
with session_scope(Session) as s:
    iter_gen = DbIterationReportGenerator(s)
    md = iter_gen.generate_iteration_markdown(workspace_id="48986738", iteration_id="1148986738001001948")
    print(md)

with session_scope(Session) as s:
    testers = ["张三", "李四"]
    tester_gen = DbTesterReportGenerator(s)
    md_path, xlsx_path = tester_gen.generate_report(
        workspace_id="48986738",
        testers=testers,
        start_date="2025-06-01",
        end_date="2025-08-31",
    )
"""

from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from typing import Dict, Iterable, List, Optional, Tuple

from sqlalchemy import and_, or_, select
import logging
DB_LOGGER = logging.getLogger("tapd_insight.report_db")

from .persistence.models import (
    TapdBug,
    TapdIteration,
    TapdStory,
    TapdStoryDeveloper,
    TapdStoryLifetime,
    TapdStoryTester,
)
from sqlalchemy.orm import Session

# ===== HTML 渲染工具：轻量主题 + 职责分离 =====

def _html_light_css() -> str:
    """
    返回用于报表的轻量化、现代化、扁平风格 CSS（浅色主题）。
    """
    return (
        ":root{--bg:#f7f8fb;--panel:#ffffff;--text:#1a2233;--muted:#5e6b7a;--accent:#2e7df6;--border:#e6e9f0;--table-row:#ffffff;--table-row-alt:#f9fbff;}"
        "*{box-sizing:border-box}"
        "body{margin:0;background:var(--bg);color:var(--text);font-family:system-ui,-apple-system,Segoe UI,Roboto,Ubuntu,'Helvetica Neue',Arial,'Noto Sans','Microsoft Yahei',sans-serif;line-height:1.6}"
        ".container{max-width:1080px;margin:0 auto;padding:28px 18px}"
        ".card{background:var(--panel);border:1px solid var(--border);border-radius:12px;box-shadow:0 8px 18px rgba(16,24,40,.06)}"
        ".header{padding:16px 20px;border-bottom:1px solid var(--border);display:flex;align-items:center;gap:10px}"
        ".header h1{margin:0;font-size:20px;font-weight:700;letter-spacing:.2px}"
        ".content{padding:16px 20px}"
        ".meta{display:flex;flex-wrap:wrap;gap:8px 16px;margin:6px 0 2px 0;color:var(--muted);font-size:13px}"
        "h1,h2,h3{color:#0f172a}"
        "h2{margin:20px 0 10px 0;font-size:18px;border-left:3px solid var(--accent);padding-left:10px}"
        ".section{margin:10px 0 18px 0;border:1px solid var(--border);border-radius:10px;overflow:hidden}"
        ".section-header{background:#fbfdff;padding:10px 14px;display:flex;align-items:center;gap:10px;border-bottom:1px solid var(--border);cursor:pointer}"
        ".section-header .title{font-weight:600}"
        ".section-body{padding:12px 14px}"
        "table{width:100%;border-collapse:separate;border-spacing:0;border:1px solid var(--border);border-radius:10px;overflow:hidden;background:var(--panel)}"
        "thead th{position:sticky;top:0;background:#f4f6fb;color:#0f172a;font-weight:600;text-align:left}"
        "th,td{padding:9px 12px;border-bottom:1px solid var(--border);font-size:14px}"
        "tbody tr:nth-child(odd){background:var(--table-row)}"
        "tbody tr:nth-child(even){background:var(--table-row-alt)}"
        "tbody tr:hover{background:#f0f6ff}"
        "th.sortable{cursor:pointer}"
        "code,pre{font-family:ui-monospace,Menlo,Monaco,Consolas,'Liberation Mono','Courier New',monospace}"
        "pre{background:#f6f8fb;padding:14px;border-radius:10px;border:1px solid var(--border);overflow:auto}"
        "a{color:var(--accent);text-decoration:none}a:hover{text-decoration:underline}"
        ".tag{display:inline-block;padding:2px 8px;border-radius:999px;background:#edf4ff;border:1px solid #d7e6ff;color:#1b3f91;font-size:12px}"
        ".toolbar{display:flex;gap:8px;align-items:center;margin-bottom:10px}"
        ".btn{background:#f5f8ff;border:1px solid var(--border);color:#334155;padding:6px 10px;border-radius:8px;font-size:13px;cursor:pointer}"
        ".btn:hover{background:#eef4ff}"
        ".search{margin-left:auto}"
        ".search input{padding:6px 10px;border:1px solid var(--border);border-radius:8px;outline:none}"
    )


def _html_interactive_js() -> str:
    """
    返回轻交互脚本：
    - h2 标题折叠区块
    - 表格列点击排序
    - 简易搜索过滤（按表格文本）
    - 若可用则用 marked 前端渲染 markdown
    """
    return (
        "(function(){\n"
        "  // 折叠区块\n"
        "  document.querySelectorAll('h2').forEach(function(h){\n"
        "    var wrapper=document.createElement('div');wrapper.className='section';\n"
        "    var header=document.createElement('div');header.className='section-header';\n"
        "    var title=document.createElement('span');title.className='title';title.textContent=h.textContent;\n"
        "    var caret=document.createElement('span');caret.textContent='▾';caret.style.color='#64748b';\n"
        "    header.appendChild(caret);header.appendChild(title);\n"
        "    var body=document.createElement('div');body.className='section-body';\n"
        "    var cursor=h.nextSibling;var fr=document.createDocumentFragment();\n"
        "    while(cursor && cursor.tagName!== 'H2'){ var nxt=cursor.nextSibling; fr.appendChild(cursor); cursor=nxt; }\n"
        "    body.appendChild(fr);\n"
        "    var parent=h.parentNode; parent.insertBefore(wrapper,h); wrapper.appendChild(header); wrapper.appendChild(body); parent.removeChild(h);\n"
        "    header.addEventListener('click',function(){ var open=body.style.display!== 'none'; body.style.display=open?'none':'block'; caret.textContent=open?'▸':'▾'; });\n"
        "  });\n"
        "  // 表格排序\n"
        "  document.querySelectorAll('table').forEach(function(table){\n"
        "    var ths=table.tHead?Array.from(table.tHead.rows[0].cells):[];\n"
        "    ths.forEach(function(th,idx){ th.classList.add('sortable'); th.addEventListener('click',function(){\n"
        "      var tbody=table.tBodies[0]; var rows=Array.from(tbody.rows); var asc=!(th._asc); th._asc=asc;\n"
        "      rows.sort(function(a,b){ var av=a.cells[idx]?a.cells[idx].innerText.trim():''; var bv=b.cells[idx]?b.cells[idx].innerText.trim():''; var na=parseFloat(av); var nb=parseFloat(bv); if(!isNaN(na)&&!isNaN(nb)){ return asc?na-nb:nb-na;} return asc?av.localeCompare(bv):bv.localeCompare(av); });\n"
        "      rows.forEach(function(r){ tbody.appendChild(r); });\n"
        "    });});\n"
        "  });\n"
        "  // 搜索过滤\n"
        "  var toolbar=document.createElement('div'); toolbar.className='toolbar';\n"
        "  var tag=document.querySelector('.tag'); if(tag){ toolbar.appendChild(tag.cloneNode(true)); }\n"
        "  var searchWrap=document.createElement('div'); searchWrap.className='search'; var input=document.createElement('input'); input.placeholder='过滤表格内容…'; searchWrap.appendChild(input); toolbar.appendChild(searchWrap);\n"
        "  var host=document.querySelector('.content'); if(host){ host.insertBefore(toolbar, host.firstChild); }\n"
        "  input.addEventListener('input',function(){\n"
        "    var kw=this.value.trim().toLowerCase();\n"
        "    document.querySelectorAll('table tbody tr').forEach(function(tr){\n"
        "      var txt=tr.innerText.toLowerCase(); tr.style.display= kw? (txt.indexOf(kw)>=0?'':'none'):'';\n"
        "    });\n"
        "  });\n"
        "  // 若引入 marked 则用前端渲染覆盖（保证更佳排版）\n"
        "  try{ var raw=document.getElementById('md-data')?.textContent; if(raw && window.marked){ var md=JSON.parse(raw); var html=window.marked.parse(md,{mangle:false,headerIds:true}); var c=document.getElementById('content'); if(c){ c.innerHTML=html; } } }catch(e){}\n"
        "})();"
    )


def _render_html_document(*, title: str, tag_label: str, markdown_text: str) -> str:
    """
    用浅色主题 + 适度交互渲染 HTML 文档。
    - 后端：若可用，优先使用 markdown 模块将 md 转换为 html
    - 前端：内置 marked 渲染作为增强与兜底
    """
    import json as _json
    html_body: str
    try:
        import markdown  # type: ignore
        html_body = markdown.markdown(markdown_text, extensions=["tables"])  # 支持表格
    except Exception:
        import html as _html
        html_body = f"<pre>{_html.escape(markdown_text)}</pre>"
    md_json = _json.dumps(markdown_text)
    css = _html_light_css()
    js = _html_interactive_js()
    doc = [
        "<!DOCTYPE html>",
        "<html lang=\"zh-CN\">",
        "<head>",
        "<meta charset=\"utf-8\">",
        "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">",
        f"<title>{title}</title>",
        f"<style>{css}</style>",
        "</head>",
        "<body>",
        "<div class=\"container\">",
        "  <div class=\"card\">",
        f"    <div class=\"header\"><span class=\"tag\">{tag_label}</span><h1>{title}</h1></div>",
        "    <div class=\"content\">",
        "      <div id=\"content\">", html_body, "</div>",
        "    </div>",
        "  </div>",
        "</div>",
        "<script id=\"md-data\" type=\"application/json\">", md_json, "</script>",
        "<script src=\"https://cdn.jsdelivr.net/npm/marked/marked.min.js\"></script>",
        f"<script>{js}</script>",
        "</body>",
        "</html>",
    ]
    return "\n".join(doc)


# 通用工具函数
def _to_float(val: Optional[Decimal | float | str]) -> float:
    """
    统一将 Decimal、str、None 转换为 float
    None 或无法解析时返回 0.0
    """
    if val is None:
        return 0.0
    try:
        if isinstance(val, Decimal):
            return float(val)
        return float(val)
    except Exception:
        return 0.0


def _is_completed_status(status_text: Optional[str]) -> bool:
    """
    业务完成判定口径
    与 iteration.py 保持一致：已投产 或 生产验收完成
    """
    st = (status_text or "").strip()
    return st in {"已投产", "生产验收完成"}


def _fmt_percent(v: Optional[float]) -> str:
    if v is None:
        return "-"
    return f"{v:.2f}%"


def _format_markdown_table_row(values: Iterable[object]) -> str:
    cells = [str(v if v is not None else "") for v in values]
    return "| " + " | ".join(cells) + " |"


# 迭代报表实现
class DbIterationReportGenerator:
    """
    基于数据库的迭代视图报表生成器
    数据源表：tapd_stories、tapd_story_testers、tapd_story_developers、tapd_story_lifetimes、tapd_iterations、tapd_bugs
    """

    def __init__(self, session: Session) -> None:
        self._s = session

    def _fetch_iteration_top_stories(self, *, workspace_id: str, iteration_id: str) -> List[TapdStory]:
        stmt = (
            select(TapdStory)
            .where(
                and_(
                    TapdStory.workspace_id == workspace_id,
                    TapdStory.iteration_id == iteration_id,
                    or_(TapdStory.parent_id.is_(None), TapdStory.parent_id == "0"),
                )
            )
            .order_by(TapdStory.created.desc())
        )
        return list(self._s.execute(stmt).scalars())

    def _fetch_people_maps(self, *, workspace_id: str, story_ids: List[str]) -> Tuple[Dict[str, List[str]], Dict[str, List[str]]]:
        if not story_ids:
            return {}, {}
        testers_stmt = (
            select(TapdStoryTester.story_id, TapdStoryTester.tester)
            .where(and_(TapdStoryTester.workspace_id == workspace_id, TapdStoryTester.story_id.in_(story_ids)))
        )
        devs_stmt = (
            select(TapdStoryDeveloper.story_id, TapdStoryDeveloper.developer)
            .where(and_(TapdStoryDeveloper.workspace_id == workspace_id, TapdStoryDeveloper.story_id.in_(story_ids)))
        )
        testers_map: Dict[str, List[str]] = {}
        for sid, tester in self._s.execute(testers_stmt).all():
            testers_map.setdefault(sid, []).append(tester)
        devs_map: Dict[str, List[str]] = {}
        for sid, dev in self._s.execute(devs_stmt).all():
            devs_map.setdefault(sid, []).append(dev)
        return testers_map, devs_map

    def _fetch_children_map(self, *, workspace_id: str, parent_ids: List[str]) -> Dict[str, List[TapdStory]]:
        if not parent_ids:
            return {}
        stmt = (
            select(TapdStory)
            .where(and_(TapdStory.workspace_id == workspace_id, TapdStory.parent_id.in_(parent_ids)))
        )
        child_map: Dict[str, List[TapdStory]] = {}
        for c in self._s.execute(stmt).scalars():
            pid = c.parent_id or ""
            child_map.setdefault(pid, []).append(c)
        return child_map

    def _fetch_lifetimes_for_stories(self, *, workspace_id: str, story_ids: List[str]) -> Dict[str, List[TapdStoryLifetime]]:
        if not story_ids:
            return {}
        stmt = (
            select(TapdStoryLifetime)
            .where(and_(TapdStoryLifetime.workspace_id == workspace_id, TapdStoryLifetime.story_id.in_(story_ids)))
        )
        lt_map: Dict[str, List[TapdStoryLifetime]] = {}
        for lt in self._s.execute(stmt).scalars():
            lt_map.setdefault(lt.story_id, []).append(lt)
        return lt_map

    def _sum_lifetime_hours_by_statuses(self, lifetimes: List[TapdStoryLifetime], statuses: Iterable[str]) -> Optional[float]:
        """
        汇总给定状态集合下的生命周期耗时总和（小时）。

        数据库可能存在重复记录，采用去重策略后再求和，以对齐 API 端语义。
        去重键为 (status, begin_date, end_date, created)。
        """
        status_set = set(statuses)
        seen: set[Tuple[Optional[str], Optional[str], Optional[str], Optional[str]]] = set()
        total = 0.0
        matched = False
        for lt in lifetimes:
            if lt.status not in status_set:
                continue
            key = (lt.status, lt.begin_date, lt.end_date, lt.created)
            if key in seen:
                continue
            seen.add(key)
            hours = _to_float(lt.time_cost_hours)
            total += hours
            matched = True
        return total if matched else None

    def generate_iteration_markdown(self, *, workspace_id: str, iteration_id: str) -> str:
        iter_row = self._s.execute(
            select(TapdIteration).where(
                and_(TapdIteration.workspace_id == workspace_id, TapdIteration.iteration_id == iteration_id)
            )
        ).scalar_one_or_none()

        if iter_row is None:
            raise ValueError(
                f"未找到迭代或无权限访问：workspace_id={workspace_id}, iteration_id={iteration_id}"
            )

        stories = self._fetch_iteration_top_stories(workspace_id=workspace_id, iteration_id=iteration_id)
        story_ids = [s.story_id for s in stories]
        testers_map, devs_map = self._fetch_people_maps(workspace_id=workspace_id, story_ids=story_ids)
        lt_map = self._fetch_lifetimes_for_stories(workspace_id=workspace_id, story_ids=story_ids)

        def to_float_size(s: TapdStory) -> float:
            return _to_float(s.size)

        total_count = len(stories)
        total_size = sum(to_float_size(s) for s in stories)
        completed_count = sum(1 for s in stories if _is_completed_status(s.status))
        incomplete_count = total_count - completed_count

        ST_REVIEW_PASSED = ["需求评审通过"]
        ST_TECH_DESIGN = ["技术方案评审"]
        ST_DEV = ["开发中", "代码评审"]
        ST_WAIT_TEST = ["已提测"]
        ST_TEST = ["测试中"]
        ST_UAT = ["待UAT验收"]
        ST_WAIT_RELEASE = ["UAT验收完成"]
        ST_PROD_ACCEPT = ["已投产"]

        def avg(values: List[Optional[float]]) -> float:
            nums = [float(v) for v in values if isinstance(v, (int, float))]
            return sum(nums) / len(nums) if nums else 0.0

        review_passed_hours = [self._sum_lifetime_hours_by_statuses(lt_map.get(s.story_id, []), ST_REVIEW_PASSED) for s in stories]
        tech_design_hours = [self._sum_lifetime_hours_by_statuses(lt_map.get(s.story_id, []), ST_TECH_DESIGN) for s in stories]
        dev_hours = [self._sum_lifetime_hours_by_statuses(lt_map.get(s.story_id, []), ST_DEV) for s in stories]
        wait_test_hours = [self._sum_lifetime_hours_by_statuses(lt_map.get(s.story_id, []), ST_WAIT_TEST) for s in stories]
        test_hours = [self._sum_lifetime_hours_by_statuses(lt_map.get(s.story_id, []), ST_TEST) for s in stories]
        uat_hours = [self._sum_lifetime_hours_by_statuses(lt_map.get(s.story_id, []), ST_UAT) for s in stories]
        wait_release_hours = [self._sum_lifetime_hours_by_statuses(lt_map.get(s.story_id, []), ST_WAIT_RELEASE) for s in stories]
        prod_accept_hours = [self._sum_lifetime_hours_by_statuses(lt_map.get(s.story_id, []), ST_PROD_ACCEPT) for s in stories]

        lines: List[str] = []
        lines.append("# 迭代概览")
        lines.append("")
        lines.append(f"- 迭代ID: {iteration_id}")
        if iter_row is not None:
            lines.append(f"- 迭代名称: {iter_row.name or ''}")
            lines.append(f"- 开始日期: {iter_row.startdate or ''}")
            lines.append(f"- 结束日期: {iter_row.enddate or ''}")
        lines.append(f"- 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("")

        lines.append("| 指标 | 数值 | 附加说明 |")
        lines.append("| --- | --- | --- |")
        lines.append(_format_markdown_table_row(["需求数量", total_count, "仅父级需求"]))
        lines.append(_format_markdown_table_row(["需求总规模", f"{total_size:.2f}", "父级需求规模求和"]))
        lines.append(_format_markdown_table_row(["完成需求数量", completed_count, "状态=已投产或生产验收完成"]))
        lines.append(_format_markdown_table_row(["未完成需求数量", incomplete_count, ""]))
        lines.append(_format_markdown_table_row(["需求评审通过平均滞留时间(小时)", f"{avg(review_passed_hours):.1f}", "仅统计有记录的需求"]))
        lines.append(_format_markdown_table_row(["平均技术方案设计时间(小时)", f"{avg(tech_design_hours):.1f}", "状态=技术方案评审"]))
        lines.append(_format_markdown_table_row(["平均开发时间(小时)", f"{avg(dev_hours):.1f}", "开发中+代码评审"]))
        lines.append(_format_markdown_table_row(["平均等待测试资源时间(小时)", f"{avg(wait_test_hours):.1f}", "状态=已提测"]))
        lines.append(_format_markdown_table_row(["平均测试时间(小时)", f"{avg(test_hours):.1f}", "状态=测试中"]))
        lines.append(_format_markdown_table_row(["平均UAT时间(小时)", f"{avg(uat_hours):.1f}", "状态=待UAT验收"]))
        lines.append(_format_markdown_table_row(["平均等待投产时长(小时)", f"{avg(wait_release_hours):.1f}", "状态=UAT验收完成"]))
        lines.append(_format_markdown_table_row(["平均生产验收时间(小时)", f"{avg(prod_accept_hours):.1f}", "状态=已投产"]))

        def group_stats(keys: List[str], refs: List[TapdStory]) -> List[Tuple[str, int, float, int, int, float, Dict[str, int], Dict[str, int]]]:
            grouped: Dict[str, Dict[str, object]] = {}
            for i, k in enumerate(keys):
                key = (k or "").strip() or "未设置"
                ref = refs[i]
                entry = grouped.setdefault(key, {"total": 0, "done": 0, "size_sum": 0.0, "priorities": {}, "sizes": {}})
                entry["total"] = int(entry["total"]) + 1
                entry["size_sum"] = float(entry["size_sum"]) + to_float_size(ref)
                if _is_completed_status(ref.status):
                    entry["done"] = int(entry["done"]) + 1
                pr = (ref.priority_label or "").strip() or "未设置"
                sizes = entry["sizes"]  # type: ignore
                prio = entry["priorities"]  # type: ignore
                prio[pr] = int(prio.get(pr, 0)) + 1  # type: ignore
                sz_key = str(ref.size) if ref.size is not None else "未设置"
                sizes[sz_key] = int(sizes.get(sz_key, 0)) + 1  # type: ignore
            result: List[Tuple[str, int, float, int, int, float, Dict[str, int], Dict[str, int]]] = []
            for k, v in sorted(grouped.items(), key=lambda kv: (-int(kv[1]["total"]), kv[0])):
                total = int(v["total"])  # type: ignore
                done = int(v["done"])  # type: ignore
                undone = total - done
                rate = (done / total * 100.0) if total > 0 else 0.0
                size_sum = float(v["size_sum"])  # type: ignore
                result.append((k, total, size_sum, done, undone, rate, v["priorities"], v["sizes"]))  # type: ignore
            return result

        def fmt_dist(d: Dict[str, int], base: int) -> str:
            if base <= 0:
                return ""
            parts = []
            for k, cnt in sorted(d.items(), key=lambda kv: (-kv[1], kv[0])):
                pct = cnt / base * 100.0
                parts.append(f"{k}:{cnt}({pct:.1f}%)")
            return ", ".join(parts)

        lines.append("")
        lines.append("# 迭代需求维度分析")
        lines.append("")
        lines.append("## 按优先级分组统计")
        lines.append("")
        lines.append("| 分组 | 总数 | 占比 | 规模总和 | 已完成 | 未完成 | 完成率 | 规模分布 |")
        lines.append("| --- | ---: | ---: | ---: | ---: | ---: | ---: | --- |")
        pr_keys = [(s.priority_label or "").strip() or "未设置" for s in stories]
        pr_stats = group_stats(pr_keys, stories)
        for k, total, size_sum, done, undone, rate, _prio, sizes in pr_stats:
            share = (total / total_count * 100.0) if total_count > 0 else 0.0
            lines.append(_format_markdown_table_row([k, total, f"{share:.1f}%", f"{size_sum:.2f}", done, undone, f"{rate:.1f}%", fmt_dist(sizes, total)]))

        lines.append("")
        lines.append("## 按规模分组统计")
        lines.append("")
        lines.append("| 分组 | 总数 | 规模总和 | 已完成 | 未完成 | 完成率 | 优先级分布 |")
        lines.append("| --- | ---: | ---: | ---: | ---: | ---: | --- |")
        size_keys = [str(s.size) if s.size is not None else "未设置" for s in stories]
        size_stats = group_stats(size_keys, stories)
        for k, total, size_sum, done, undone, rate, prio, _sizes in size_stats:
            lines.append(_format_markdown_table_row([k, total, f"{size_sum:.2f}", done, undone, f"{rate:.1f}%", fmt_dist(prio, total)]))

        lines.append("")
        lines.append("## 按产品经理分组统计")
        lines.append("")
        lines.append("| 分组 | 总数 | 规模总和 | 已完成 | 未完成 | 完成率 | 规模分布 | 优先级分布 |")
        lines.append("| --- | ---: | ---: | ---: | ---: | ---: | --- | --- |")
        pm_keys = [(s.product_manager or "").strip() or "未设置" for s in stories]
        pm_stats = group_stats(pm_keys, stories)
        for k, total, size_sum, done, undone, rate, prio, sizes in pm_stats:
            lines.append(_format_markdown_table_row([k, total, f"{size_sum:.2f}", done, undone, f"{rate:.1f}%", fmt_dist(sizes, total), fmt_dist(prio, total)]))

        lines.append("")
        lines.append("## 按模块分组统计")
        lines.append("")
        lines.append("| 分组 | 总数 | 规模总和 | 已完成 | 未完成 | 完成率 | 优先级分布 |")
        lines.append("| --- | ---: | ---: | ---: | ---: | ---: | --- |")
        module_keys = [(s.module or "").strip() or "未设置" for s in stories]
        module_stats = group_stats(module_keys, stories)
        for k, total, size_sum, done, undone, rate, prio, _sizes in module_stats:
            lines.append(_format_markdown_table_row([k, total, f"{size_sum:.2f}", done, undone, f"{rate:.1f}%", fmt_dist(prio, total)]))

        lines.append("")
        lines.append("# 未完成需求分析")
        lines.append("")
        status_to_stories: Dict[str, List[TapdStory]] = {}
        for s in stories:
            if _is_completed_status(s.status):
                continue
            key = (s.status or "").strip() or "未设置"
            status_to_stories.setdefault(key, []).append(s)

        def fmt_opt(v: Optional[float]) -> str:
            return f"{float(v):.1f}" if isinstance(v, (int, float, float)) else ""

        for st, lst in sorted(status_to_stories.items(), key=lambda kv: kv[0]):
            lines.append(f"## 状态：{st}")
            lines.append("")
            lines.append("| 需求标题 | 规模 | 优先级 | 产品经理 | 研发负责人 | 开发者 | 测试负责人 | 测试人员 | 需求提出时间 | 需求评审通过时间 | 评审通过滞留(h) | 技术方案(h) | 开发(h) | 等待测试(h) | 测试(h) | UAT(h) | 等待投产(h) | 生产验收(h) | 未完成原因 |")
            lines.append("| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | ---: | ---: | ---: | ---: | ---: | ---: | ---: | ---: | --- |")
            for s in lst:
                name = s.name or ""
                size_val = str(s.size) if s.size is not None else ""
                pr_val = s.priority_label or ""
                pm_val = s.product_manager or "未设置"
                tech_lead = s.tech_lead or ""
                devs = ", ".join(sorted(set(devs_map.get(s.story_id, []))))
                test_lead = s.test_lead or ""
                testers = ", ".join(sorted(set(testers_map.get(s.story_id, []))))
                created = s.created or ""
                review_passed = s.review_passed or ""
                lts = lt_map.get(s.story_id, [])
                _r = self._sum_lifetime_hours_by_statuses(lts, ST_REVIEW_PASSED)
                _t = self._sum_lifetime_hours_by_statuses(lts, ST_TECH_DESIGN)
                _d = self._sum_lifetime_hours_by_statuses(lts, ST_DEV)
                _wt = self._sum_lifetime_hours_by_statuses(lts, ST_WAIT_TEST)
                _ts = self._sum_lifetime_hours_by_statuses(lts, ST_TEST)
                _uat = self._sum_lifetime_hours_by_statuses(lts, ST_UAT)
                _wr = self._sum_lifetime_hours_by_statuses(lts, ST_WAIT_RELEASE)
                _pa = self._sum_lifetime_hours_by_statuses(lts, ST_PROD_ACCEPT)
                lines.append(_format_markdown_table_row([
                    name,
                    size_val,
                    pr_val,
                    pm_val,
                    tech_lead,
                    devs,
                    test_lead,
                    testers,
                    str(created),
                    str(review_passed),
                    fmt_opt(_r),
                    fmt_opt(_t),
                    fmt_opt(_d),
                    fmt_opt(_wt),
                    fmt_opt(_ts),
                    fmt_opt(_uat),
                    fmt_opt(_wr),
                    fmt_opt(_pa),
                    "",
                ]))

        lines.append("")
        lines.append("# 附录：需求明细")
        lines.append("")
        lines.append("| 需求名称 | 需求id | 规模 | 优先级 | 产品经理 | 需求创建时间 | 需求评审通过时间 | 需求状态 | 需求研发负责人 | 需求测试负责人 | 评审通过滞留(h) | 技术方案(h) | 开发(h) | 等待测试(h) | 测试(h) | UAT(h) | 等待投产(h) | 生产验收(h) |")
        lines.append("| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | ---: | ---: | ---: | ---: | ---: | ---: | ---: | ---: |")
        stories_sorted = sorted(stories, key=lambda s: (-_to_float(s.size), s.name or ""))
        for s in stories_sorted:
            name = s.name or ""
            sid = s.story_id
            size_val = str(s.size) if s.size is not None else ""
            pr_val = s.priority_label or ""
            pm_val = s.product_manager or ""
            created = s.created or ""
            review_passed = s.review_passed or ""
            status_text = s.status or ""
            tech_lead = s.tech_lead or ""
            test_lead = s.test_lead or ""
            lts = lt_map.get(s.story_id, [])
            _r = self._sum_lifetime_hours_by_statuses(lts, ST_REVIEW_PASSED)
            _t = self._sum_lifetime_hours_by_statuses(lts, ST_TECH_DESIGN)
            _d = self._sum_lifetime_hours_by_statuses(lts, ST_DEV)
            _wt = self._sum_lifetime_hours_by_statuses(lts, ST_WAIT_TEST)
            _ts = self._sum_lifetime_hours_by_statuses(lts, ST_TEST)
            _uat = self._sum_lifetime_hours_by_statuses(lts, ST_UAT)
            _wr = self._sum_lifetime_hours_by_statuses(lts, ST_WAIT_RELEASE)
            _pa = self._sum_lifetime_hours_by_statuses(lts, ST_PROD_ACCEPT)
            lines.append(_format_markdown_table_row([
                name,
                sid,
                size_val,
                pr_val,
                pm_val,
                str(created),
                str(review_passed),
                status_text,
                tech_lead,
                test_lead,
                f"{_r:.1f}" if _r is not None else "",
                f"{_t:.1f}" if _t is not None else "",
                f"{_d:.1f}" if _d is not None else "",
                f"{_wt:.1f}" if _wt is not None else "",
                f"{_ts:.1f}" if _ts is not None else "",
                f"{_uat:.1f}" if _uat is not None else "",
                f"{_wr:.1f}" if _wr is not None else "",
                f"{_pa:.1f}" if _pa is not None else "",
            ]))

        return "\n".join(lines)

    def generate_iteration_html(self, *, workspace_id: str, iteration_id: str) -> str:
        """
        生成与 Markdown 报表等价的 HTML 内容（浅色主题 + 轻交互）。
        """
        md = self.generate_iteration_markdown(workspace_id=workspace_id, iteration_id=iteration_id)
        return _render_html_document(title="迭代报表", tag_label="Iteration Report", markdown_text=md)


# 测试团队报表实现
@dataclass
class TeamOverviewMetrics:
    total_stories: int
    total_size_points: float
    total_valid_bugs: int
    total_invalid_bugs: int
    fat_bugs: int
    uat_bugs: int
    prod_verify_bugs: int
    prod_bugs: int
    invalid_rate_percent: Optional[float]
    uat_escape_rate_percent: Optional[float]
    prod_escape_rate_percent: Optional[float]


@dataclass
class PersonalMetrics:
    tester: str
    total_stories: int
    total_size_points: float
    total_valid_bugs: int
    total_invalid_bugs: int
    fat_bugs: int
    uat_bugs: int
    prod_verify_bugs: int
    prod_bugs: int
    invalid_rate_percent: Optional[float]
    uat_escape_rate_percent: Optional[float]
    prod_escape_rate_percent: Optional[float]


class DbTesterReportGenerator:
    """
    基于数据库的测试团队报表生成器
    """

    def __init__(self, session: Session) -> None:
        self._s = session

    def _fetch_stories_for_tester(self, *, workspace_id: str, tester: str, start_date: Optional[str], end_date: Optional[str]) -> List[TapdStory]:
        conds = []
        if start_date:
            conds.append(TapdStory.created >= start_date)
        if end_date:
            conds.append(TapdStory.created < end_date)
        # 先查 tester 对应的 story_id 列表，再按 id 拉取 Story，避免 SQL JOIN
        ids_stmt = (
            select(TapdStoryTester.story_id)
            .where(and_(TapdStoryTester.workspace_id == workspace_id, TapdStoryTester.tester == tester))
        )
        story_id_rows = self._s.execute(ids_stmt).all()
        story_ids = [r[0] for r in story_id_rows if r and r[0]]
        if not story_ids:
            return []
        stmt = (
            select(TapdStory)
            .where(and_(TapdStory.workspace_id == workspace_id, TapdStory.story_id.in_(story_ids), *conds))
            .order_by(TapdStory.created.desc())
        )
        return list(self._s.execute(stmt).scalars())

    def _collect_top_stories(self, stories: List[TapdStory]) -> List[TapdStory]:
        tops: Dict[str, TapdStory] = {}
        for s in stories:
            has_parent = s.parent_id is not None and s.parent_id != "0"
            if not has_parent:
                if s.story_id not in tops:
                    tops[s.story_id] = s
        return list(tops.values())

    def _fetch_children_for_parents(self, *, workspace_id: str, parent_ids: List[str]) -> Dict[str, List[TapdStory]]:
        return DbIterationReportGenerator(self._s)._fetch_children_map(workspace_id=workspace_id, parent_ids=parent_ids)

    def _bugs_aggregate_for_story_ids(self, *, workspace_id: str, story_ids: List[str]) -> Tuple[int, int, int, int, int, int]:
        if not story_ids:
            return 0, 0, 0, 0, 0, 0
        # 仅查询必要字段，避免在 SQL 中做复杂聚合
        stmt = (
            select(TapdBug.is_valid, TapdBug.env_normalized)
            .where(
                and_(
                    TapdBug.workspace_id == workspace_id,
                    TapdBug.story_id.is_not(None),
                    TapdBug.story_id.in_(story_ids),
                )
            )
        )
        valid_bugs = 0
        invalid_bugs = 0
        fat_bugs = 0
        uat_bugs = 0
        prod_verify_bugs = 0
        prod_bugs = 0
        for is_valid, env in self._s.execute(stmt).all():
            if is_valid is True:
                valid_bugs += 1
                if env == "FAT":
                    fat_bugs += 1
                elif env == "UAT":
                    uat_bugs += 1
                elif env == "PROD_VERIFY":
                    prod_verify_bugs += 1
                elif env == "PROD":
                    prod_bugs += 1
            elif is_valid is False:
                invalid_bugs += 1
            else:
                # None 或未知有效性不计入任何一类
                pass
        return valid_bugs, invalid_bugs, fat_bugs, uat_bugs, prod_verify_bugs, prod_bugs

    @staticmethod
    def _calc_rates(valid_bugs: int, invalid_bugs: int, fat_bugs: int, uat_bugs: int, prod_verify_bugs: int, prod_bugs: int) -> Tuple[Optional[float], Optional[float], Optional[float]]:
        total_bugs = valid_bugs + invalid_bugs
        invalid_rate = None
        if total_bugs > 0:
            invalid_rate = float(invalid_bugs) / float(total_bugs) * 100.0
        uat_den = uat_bugs + fat_bugs
        uat_escape_rate = None
        if uat_den > 0:
            uat_escape_rate = float(uat_bugs) / float(uat_den) * 100.0
        prod_den = valid_bugs
        prod_escape_rate = None
        if prod_den > 0:
            prod_escape_rate = float(prod_verify_bugs + prod_bugs) / float(prod_den) * 100.0
        return invalid_rate, uat_escape_rate, prod_escape_rate

    def _team_overview(self, *, workspace_id: str, testers_to_stories: Dict[str, List[TapdStory]]) -> TeamOverviewMetrics:
        all_stories: Dict[str, TapdStory] = {}
        for lst in testers_to_stories.values():
            for s in lst:
                if s.story_id not in all_stories:
                    all_stories[s.story_id] = s
        top_stories = self._collect_top_stories(list(all_stories.values()))
        total_stories = len(top_stories)
        total_size = sum(_to_float(s.size) for s in top_stories)

        parent_ids = [s.story_id for s in top_stories]
        children_map = self._fetch_children_for_parents(workspace_id=workspace_id, parent_ids=parent_ids)
        story_ids_for_bugs: List[str] = []
        for p in parent_ids:
            story_ids_for_bugs.append(p)
            for c in children_map.get(p, []):
                story_ids_for_bugs.append(c.story_id)

        valid_bugs, invalid_bugs, fat_bugs, uat_bugs, prod_verify_bugs, prod_bugs = self._bugs_aggregate_for_story_ids(
            workspace_id=workspace_id, story_ids=story_ids_for_bugs
        )
        invalid_rate, uat_escape_rate, prod_escape_rate = self._calc_rates(valid_bugs, invalid_bugs, fat_bugs, uat_bugs, prod_verify_bugs, prod_bugs)

        return TeamOverviewMetrics(
            total_stories=total_stories,
            total_size_points=total_size,
            total_valid_bugs=valid_bugs,
            total_invalid_bugs=invalid_bugs,
            fat_bugs=fat_bugs,
            uat_bugs=uat_bugs,
            prod_verify_bugs=prod_verify_bugs,
            prod_bugs=prod_bugs,
            invalid_rate_percent=invalid_rate,
            uat_escape_rate_percent=uat_escape_rate,
            prod_escape_rate_percent=prod_escape_rate,
        )

    def _personal_metrics(self, *, workspace_id: str, tester: str, stories: List[TapdStory]) -> PersonalMetrics:
        top_stories = self._collect_top_stories(stories)
        parent_ids = [s.story_id for s in top_stories]
        children_map = self._fetch_children_for_parents(workspace_id=workspace_id, parent_ids=parent_ids)

        total_stories = 0
        total_size = 0.0
        for s in top_stories:
            total_stories += 1 + len(children_map.get(s.story_id, []))
            total_size += _to_float(s.size)
            for c in children_map.get(s.story_id, []):
                total_size += _to_float(c.size)

        story_ids_for_bugs: List[str] = []
        for p in parent_ids:
            story_ids_for_bugs.append(p)
            for c in children_map.get(p, []):
                story_ids_for_bugs.append(c.story_id)

        valid_bugs, invalid_bugs, fat_bugs, uat_bugs, prod_verify_bugs, prod_bugs = self._bugs_aggregate_for_story_ids(
            workspace_id=workspace_id, story_ids=story_ids_for_bugs
        )
        invalid_rate, uat_escape_rate, prod_escape_rate = self._calc_rates(valid_bugs, invalid_bugs, fat_bugs, uat_bugs, prod_verify_bugs, prod_bugs)

        return PersonalMetrics(
            tester=tester,
            total_stories=total_stories,
            total_size_points=total_size,
            total_valid_bugs=valid_bugs,
            total_invalid_bugs=invalid_bugs,
            fat_bugs=fat_bugs,
            uat_bugs=uat_bugs,
            prod_verify_bugs=prod_verify_bugs,
            prod_bugs=prod_bugs,
            invalid_rate_percent=invalid_rate,
            uat_escape_rate_percent=uat_escape_rate,
            prod_escape_rate_percent=prod_escape_rate,
        )

    def _build_person_df(self, *, workspace_id: str, tester: str, stories: List[TapdStory]):
        try:
            import pandas as pd
        except Exception as e:
            raise RuntimeError("需要 pandas 才能导出 Excel") from e

        top_stories = self._collect_top_stories(stories)
        parent_ids = [s.story_id for s in top_stories]
        children_map = self._fetch_children_for_parents(workspace_id=workspace_id, parent_ids=parent_ids)

        rows: List[Dict[str, object]] = []
        for s in top_stories:
            story_ids = [s.story_id] + [c.story_id for c in children_map.get(s.story_id, [])]
            valid_bugs, invalid_bugs, fat_bugs, uat_bugs, prod_verify_bugs, prod_bugs = self._bugs_aggregate_for_story_ids(
                workspace_id=workspace_id, story_ids=story_ids
            )
            invalid_rate, uat_escape_rate, prod_escape_rate = self._calc_rates(valid_bugs, invalid_bugs, fat_bugs, uat_bugs, prod_verify_bugs, prod_bugs)
            rows.append({
                "StoryID": s.story_id,
                "Name": s.name or "",
                "Created": s.created or "",
                "Size": _to_float(s.size),
                "HasChildren": len(children_map.get(s.story_id, [])) > 0,
                "ValidBugs": valid_bugs,
                "InvalidBugs": invalid_bugs,
                "FAT": fat_bugs,
                "UAT": uat_bugs,
                "PROD_VERIFY": prod_verify_bugs,
                "PROD": prod_bugs,
                "InvalidRate%": round(invalid_rate, 2) if invalid_rate is not None else None,
                "UAT_Escape%": round(uat_escape_rate, 2) if uat_escape_rate is not None else None,
                "PROD_Escape%": round(prod_escape_rate, 2) if prod_escape_rate is not None else None,
            })
        import pandas as pd  # 保证类型提示
        return pd.DataFrame(rows)

    def generate_report(self, *, workspace_id: str, testers: List[str], start_date: Optional[str], end_date: Optional[str], output_dir: Optional[str] = None) -> Tuple[str, str]:
        import os
        from datetime import datetime as _dt

        output_dir = output_dir or os.path.join(os.getcwd(), "reports")
        os.makedirs(output_dir, exist_ok=True)

        testers_to_stories: Dict[str, List[TapdStory]] = {}
        for tester in testers:
            testers_to_stories[tester] = self._fetch_stories_for_tester(
                workspace_id=workspace_id, tester=tester, start_date=start_date, end_date=end_date
            )

        team_metrics = self._team_overview(workspace_id=workspace_id, testers_to_stories=testers_to_stories)

        personals: List[PersonalMetrics] = []
        for tester, stories in testers_to_stories.items():
            personals.append(self._personal_metrics(workspace_id=workspace_id, tester=tester, stories=stories))

        now_str = _dt.now().strftime("%Y%m%d_%H%M%S")
        md_path = os.path.join(output_dir, f"tester_report_{now_str}.md")
        xlsx_path = os.path.join(output_dir, f"tester_report_{now_str}.xlsx")
        html_path = os.path.join(output_dir, f"tester_report_{now_str}.html")

        lines: List[str] = []
        lines.append("# 测试团队报表")
        lines.append("")
        lines.append(f"时间范围：{start_date or '-'} 至 {end_date or '-'}")
        lines.append(f"测试人员：{', '.join(testers)}")
        lines.append("")
        lines.append("## 一、团队总览指标")
        lines.append("")
        lines.append(f"- 总需求数（不含子需求）：{team_metrics.total_stories}")
        lines.append(f"- 团队总规模（不含子需求）：{team_metrics.total_size_points:.2f}")
        lines.append(f"- 缺陷分布（有效）：FAT={team_metrics.fat_bugs}，UAT={team_metrics.uat_bugs}，生产验收={team_metrics.prod_verify_bugs}，生产={team_metrics.prod_bugs}")
        lines.append(f"- 团队无效缺陷率：{_fmt_percent(team_metrics.invalid_rate_percent)}")
        lines.append(f"- 团队UAT缺陷逃逸率：{_fmt_percent(team_metrics.uat_escape_rate_percent)}")
        lines.append(f"- 团队总生产缺陷逃逸率：{_fmt_percent(team_metrics.prod_escape_rate_percent)}")
        lines.append("")
        lines.append("## 二、个人指标")
        lines.append("")
        lines.append("| 测试人员 | 需求数(含子) | 总规模(含子) | FAT | UAT | 生产验收 | 生产 | 无效缺陷率 | UAT缺陷逃逸率 | 总生产缺陷逃逸率 |")
        lines.append("| --- | ---: | ---: | ---: | ---: | ---: | ---: | ---: | ---: | ---: |")
        for pm in personals:
            lines.append(_format_markdown_table_row([
                pm.tester,
                pm.total_stories,
                f"{pm.total_size_points:.2f}",
                pm.fat_bugs,
                pm.uat_bugs,
                pm.prod_verify_bugs,
                pm.prod_bugs,
                _fmt_percent(pm.invalid_rate_percent),
                _fmt_percent(pm.uat_escape_rate_percent),
                _fmt_percent(pm.prod_escape_rate_percent),
            ]))

        md_text = "\n".join(lines)
        with open(md_path, "w", encoding="utf-8") as f:
            f.write(md_text)

        # 额外输出 HTML（浅色主题 + 轻交互）
        html_text = _render_html_document(title="测试团队报表", tag_label="Tester Report", markdown_text=md_text)
        with open(html_path, "w", encoding="utf-8") as f:
            f.write(html_text)

        try:
            import pandas as pd
        except Exception:
            return md_path, ""

        with pd.ExcelWriter(xlsx_path, engine="openpyxl") as writer:
            for tester, stories in testers_to_stories.items():
                df = self._build_person_df(workspace_id=workspace_id, tester=tester, stories=stories)
                sheet_name = tester[:31] if tester else "Sheet"
                df.to_excel(writer, index=False, sheet_name=sheet_name)

        return md_path, xlsx_path



# ===== Demo & __main__ 入口 =====
def demo_iteration_report_db() -> None:
    """
    基于数据库生成迭代报表的演示。

    需要先在本地 MySQL 中准备好数据。请根据实际情况修改 db_url。
    若无法从数据库定位到任何迭代，将回退到示例迭代ID。
    """
    import os
    from .config import load_tapd_config
    from .persistence.db import create_session_factory, session_scope

    logger = logging.getLogger("tapd_insight.report_db")

    cfg = load_tapd_config()
    db_url = "mysql+pymysql://team_seek:seek#<EMAIL>:3306/team_seek"
    logger.warning("demo_iteration_report_db: 请根据本地数据库设置修改 db_url 后再运行")

    Session = create_session_factory(db_url)
    with session_scope(Session) as s:
        gen = DbIterationReportGenerator(s)
        iteration_id = "1148986738001001961"
        md = gen.generate_iteration_markdown(workspace_id=str(cfg.workspace_id), iteration_id=iteration_id)
        ts = datetime.now().strftime("%Y%m%d_%H%M%S")
        out_dir = os.path.join(os.getcwd(), "reports")
        os.makedirs(out_dir, exist_ok=True)
        out_path = os.path.join(out_dir, f"db_iteration_{iteration_id}_{ts}.md")
        with open(out_path, "w", encoding="utf-8") as f:
            f.write(md)
        logger.info(f"迭代报表已输出：{out_path}")
        # 额外输出 HTML 报表
        html = gen.generate_iteration_html(workspace_id=str(cfg.workspace_id), iteration_id=iteration_id)
        html_path = os.path.join(out_dir, f"db_iteration_{iteration_id}_{ts}.html")
        with open(html_path, "w", encoding="utf-8") as f:
            f.write(html)
        logger.info(f"迭代报表(HTML)已输出：{html_path}")


def demo_tester_report_db() -> None:
    """
    基于数据库生成测试团队报表的演示。

    自动从数据库选择工作空间下最常见的前5位测试人员；若无数据，将回退到占位名单。
    需要先在本地 MySQL 中准备好数据。请根据实际情况修改 db_url。
    """
    import os
    from datetime import timedelta
    from .config import load_tapd_config
    from .persistence.db import create_session_factory, session_scope

    logger = logging.getLogger("tapd_insight.report_db")

    cfg = load_tapd_config()
    db_url = "mysql+pymysql://team_seek:seek#<EMAIL>:3306/team_seek"
    logger.warning("demo_tester_report_db: 请根据本地数据库设置修改 db_url 后再运行")

    Session = create_session_factory(db_url)
    with session_scope(Session) as s:
        testers = ["毛浩", "陈广发", "陈赞旭", "谢杰明", "周佩佩", "张雯慧"]
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=90)).strftime("%Y-%m-%d")

        gen = DbTesterReportGenerator(s)
        md_path, xlsx_path = gen.generate_report(
            workspace_id=str(cfg.workspace_id),
            testers=testers,
            start_date=start_date,
            end_date=end_date,
            output_dir=os.path.join(os.getcwd(), "reports"),
        )
        logger.info(f"测试团队 Markdown 报表：{md_path}")
        if xlsx_path:
            logger.info(f"测试团队 Excel 明细：{xlsx_path}")
        else:
            logger.info("未安装 pandas/openpyxl，未生成 Excel 明细")


def demo_iteration_markdown_db() -> None:
    """
    演示：仅输出迭代报表的 Markdown 文本到文件，便于测试 generate_iteration_markdown。
    """
    import os
    from .config import load_tapd_config
    from .persistence.db import create_session_factory, session_scope
    from datetime import datetime as _dt

    logger = logging.getLogger("tapd_insight.report_db")

    cfg = load_tapd_config()
    db_url = "mysql+pymysql://team_seek:seek#<EMAIL>:3306/team_seek"
    logger.warning("demo_iteration_markdown_db: 请根据本地数据库设置修改 db_url 后再运行")

    Session = create_session_factory(db_url)
    with session_scope(Session) as s:
        gen = DbIterationReportGenerator(s)
        iteration_id = "1111"
        md = gen.generate_iteration_markdown(workspace_id=str(cfg.workspace_id), iteration_id=iteration_id)
        ts = _dt.now().strftime("%Y%m%d_%H%M%S")
        out_dir = os.path.join(os.getcwd(), "reports")
        os.makedirs(out_dir, exist_ok=True)
        md_path = os.path.join(out_dir, f"db_iteration_md_{iteration_id}_{ts}.md")
        with open(md_path, "w", encoding="utf-8") as f:
            f.write(md)
        logger.info(f"迭代 Markdown 报表已输出：{md_path}")


if __name__ == "__main__":
    from tapd_insight.config import setup_tapd_logging
    setup_tapd_logging("INFO")

    demo_iteration_markdown_db()

    # try:
    #     demo_iteration_report_db()
    # except Exception:
    #     logging.getLogger("tapd_insight.report_db").exception("迭代报表演示失败")
    # try:
    #     demo_tester_report_db()
    # except Exception:
    #     logging.getLogger("tapd_insight.report_db").exception("测试团队报表演示失败")
    # try:
    #     demo_iteration_markdown_db()
    # except Exception:
    #     logging.getLogger("tapd_insight.report_db").exception("迭代Markdown报表演示失败")
