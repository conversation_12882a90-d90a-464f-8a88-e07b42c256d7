from django.http import HttpRequest, HttpResponse
from django.shortcuts import render
import os

from tapd_insight.persistence.db import create_session_factory, session_scope
from tapd_insight.report_db import DbIterationReportGenerator


def _get_db_url() -> str:
    url = os.environ.get("TAPD_DB_URL")
    if url:
        return url
    # 默认本地演示库（请改为你的环境）
    return "mysql+pymysql://team_seek:seek#<EMAIL>:3306/team_seek"


def home(request: HttpRequest) -> HttpResponse:
    if request.method == "GET":
        return render(request, "home.html", {})
    # 支持简单表单 POST 搜索
    iteration_id = request.POST.get("iteration_id", "").strip()
    if not iteration_id:
        return render(request, "home.html", {"error": "请输入迭代ID"})
    return iteration_report_view(request)


def iteration_report_view(request: HttpRequest) -> HttpResponse:
    iteration_id = request.GET.get("iteration_id") or request.POST.get("iteration_id")
    iteration_id = (iteration_id or "").strip()
    if not iteration_id:
        return render(request, "home.html", {"error": "请输入迭代ID"})

    workspace_id = os.environ.get("TAPD_WORKSPACE_ID", "48986738")
    if not workspace_id:
        return render(request, "home.html", {"error": "未设置 TAPD_WORKSPACE_ID 环境变量"})

    Session = create_session_factory(_get_db_url())
    with session_scope(Session) as s:
        gen = DbIterationReportGenerator(s)
        html = gen.generate_iteration_html(workspace_id=str(workspace_id), iteration_id=str(iteration_id))
    return HttpResponse(html)


