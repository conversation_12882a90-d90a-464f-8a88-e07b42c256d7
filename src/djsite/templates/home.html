<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Tapd Insight - 报表首页</title>
    <style>
      body {
        margin: 0;
        background: #f7f8fb;
        color: #1a2233;
        font-family: system-ui, -apple-system, Segoe UI, Roboto, Ubuntu,
          "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Microsoft Yahei", sans-serif;
      }
      .wrap {
        max-width: 880px;
        margin: 0 auto;
        padding: 28px 18px;
      }
      .card {
        background: #fff;
        border: 1px solid #e6e9f0;
        border-radius: 12px;
        box-shadow: 0 8px 18px rgba(16, 24, 40, 0.06);
      }
      .hd {
        padding: 16px 20px;
        border-bottom: 1px solid #e6e9f0;
        display: flex;
        align-items: center;
        gap: 10px;
      }
      .hd h1 {
        margin: 0;
        font-size: 20px;
      }
      .bd {
        padding: 16px 20px;
      }
      .row {
        display: flex;
        gap: 10px;
        align-items: center;
      }
      input[type="text"] {
        flex: 1;
        padding: 10px;
        border: 1px solid #e6e9f0;
        border-radius: 8px;
        outline: none;
      }
      button {
        padding: 10px 14px;
        border-radius: 8px;
        border: 1px solid #e6e9f0;
        background: #f5f8ff;
        color: #334155;
        cursor: pointer;
      }
      button:hover {
        background: #eef4ff;
      }
      .hint {
        margin-top: 12px;
        color: #5e6b7a;
        font-size: 13px;
      }
      .err {
        color: #d14343;
        margin: 8px 0;
      }
    </style>
  </head>
  <body>
    <div class="wrap">
      <div class="card">
        <div class="hd"><h1>迭代报表查询</h1></div>
        <div class="bd">
          {% if error %}
          <div class="err">{{ error }}</div>
          {% endif %}
          <form method="get" action="/report/iteration/">
            <div class="row">
              <input
                type="text"
                name="iteration_id"
                placeholder="请输入迭代ID，例如：1148986738001001961"
              />
              <button type="submit">生成报表</button>
            </div>
          </form>
          <div class="hint">
            提示：需事先在环境变量中设置
            <code>TAPD_WORKSPACE_ID</code> 与可访问的数据库连接
            <code>TAPD_DB_URL</code>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
